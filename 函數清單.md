# 專案：tribe-line-bot 函數清單

## 📋 文檔版本
- **更新日期**: 2025-07-17
- **版本**: v2.6.0
- **架構**: 活動日誌修復完成 - 函數名稱衝突防護
- **重構成果**: 修復 doPost 函數覆蓋問題，建立函數重複檢查機制

## 🏗️ 核心架構模組

### Core 核心模組（v1.5.0 重構後）
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `core_main.gs` | `systemHealthCheck()` | 系統健康檢查 |
| | `reinitializeSystem()` | 重新初始化整個系統 |
| | `getVersionInfo()` | 獲取版本資訊 |
| `config_core.gs` | `initializeSheets()` | 初始化所有工作表 |
| | `setupAPIKEYSheetSafe()` | 安全設定APIKEY工作表 |
| | `setupSheetHeaders()` | 設定工作表標題 |

### Core Utils 模組（v1.5.0 重構拆分）
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `core_utils_config.gs` | `getConfig()` | 獲取系統配置 |
| | `getConfigValue()` | 動態讀取配置值 |
| | `getCurrentGeminiApiKey()` | 獲取當前 Gemini API Key |
| `core_utils_api.gs` | `callGemini()` | 統一Gemini API調用 |
| | `getModelForTask()` | 智能模型選擇 |
| | `callGeminiWithIntent()` | 帶意圖的 API 調用 |
| `core_utils_helpers.gs` | `normalizeText()` | 文字正規化 |
| | `normalizeCommand()` | 指令正規化 |
| | `isFeatureToggleEnabled()` | 功能開關檢查 |
| `core_utils_reply.gs` | `replyMessage()` | 基礎訊息回覆 |
| | `replyWithAudio()` | 音頻回覆 |
| | `replyWithImage()` | 圖片回覆 |
| `core_utils_logging.gs` | `logActivity()` | 記錄活動日誌 |
| | `getFileTypeInfo()` | 獲取檔案類型資訊 |
| | `findRecentImageHistory()` | 查找用戶最近圖片記錄 |

### Core Module Manager 模組（v1.5.0 重構拆分）
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `core_module_mapping.gs` | `getModuleFileMapping()` | 獲取模組檔案映射表 |
| | `getModuleInfo()` | 獲取特定模組資訊 |
| | `getAllFeatureNames()` | 獲取所有功能名稱 |
| `core_module_manager_core.gs` | `autoManageModules()` | 自動模組管理 |
| | `activateModule()` | 啟用模組 |
| | `deactivateModule()` | 停用模組 |
| `core_module_file_ops.gs` | `moveFileToRoot()` | 移動檔案到根目錄 |
| | `moveFileToSleeping()` | 移動檔案到 _sleeping |
| | `checkFileExists()` | 檢查檔案存在性 |
| `core_module_safety.gs` | `runSafetyNetCheck()` | 執行安全網檢查 |
| | `checkFileConsistency()` | 檢查檔案一致性 |
| | `findMissingFiles()` | 查找缺失檔案 |

### Core Feature Toggle 模組
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `core_feature_toggle.gs` | `isFeatureEnabled()` | 檢查功能是否啟用 |
| | `getAllFeatureStates()` | 獲取所有功能狀態 |
| | `validateFeatureToggleConsistency()` | 驗證功能開關一致性 |

## 🎯 LINE Bot 處理模組

### LINE Webhook 核心模組（v1.5.0 重構）
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_line_webhook_core.gs` | `doGet()` | HTTP GET 入口點 |
| | `doPost()` | LINE Webhook 主入口 |
| | `generateSystemStatusHTML()` | 生成系統狀態頁面 |
| | `extractEventInfo()` | 提取事件基本資訊 |
| `modules_line_webhook_group.gs` | `handleGroupMessage()` | 群組訊息處理 |
| | `shouldRespondToGroupMessage()` | 智能群組回應判斷 |
| | `recordGroupMessageBackup()` | 備用群組記錄方法 |
| `modules_line_webhook_private.gs` | `handlePrivateMessage()` | 私人訊息處理 |
| | `routeMessageByType()` | 訊息類型路由 |
| | `handleImageMessage()` | 圖片訊息處理 |
| | `handleVideoMessage()` | 影片訊息處理 |
| | `handleAudioMessage()` | 音頻訊息處理 |
| `modules_line_webhook_postback.gs` | `handlePostbackMessage()` | Postback 事件處理 |
| | `handleNewPostbackAction()` | 新按鈕動作處理 |
| | `parsePostbackData()` | 解析 Postback 數據 |
| `modules_line_webhook_events.gs` | `routeEventByType()` | 事件類型路由 |
| | `handleFollowEvent()` | 用戶關注事件 |
| | `handleJoinEvent()` | 加入群組事件 |
| | `handleMemberJoinedEvent()` | 群組成員加入事件 |

### AI-First 處理系統
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_line_processor_core.gs` | `handleTextMessageAIFirst()` | AI-First文字處理 |
| | `routeByAIIntent()` | AI意圖路由 |
| | `handleSpecialResponseV2()` | 特殊回覆處理 |

## 🤖 AI 提示詞模組（v1.5.0 重構）

### AI 提示詞核心模組
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_ai_prompts_core.gs` | `callAIWithPrompt()` | 統一提示詞調用接口 |
| | `buildPrompt()` | 提示詞模板建構器 |
| | `detectResponseScenario()` | 智能場景檢測 |
| | `getResponseLengthGuidance()` | 獲取回應長度指引 |
| `modules_ai_prompts_intent.gs` | `analyzeUserIntent()` | 意圖分析輔助函數 |
| | `parseUserQuery()` | 查詢解析輔助函數 |
| | `generateSearchResponse()` | 語義搜索回應生成 |
| | `triggerUnifiedGuidance()` | 智能引導觸發 |
| `modules_ai_prompts_image.gs` | `generateImagePrompt()` | 圖像提示詞生成 |
| | `analyzeTTSMode()` | TTS 模式分析 |
| | `extractTTSText()` | TTS 文字提取 |
| | `routeImagePrompt()` | 智能提示詞路由 |
| `modules_ai_prompts_character.gs` | `getCharacterProfileForPrompt()` | 角色設定獲取 |
| | `buildPersonalizedPrompt()` | 動態個性化提示詞生成 |
| | `getGroupChatProfile()` | 群聊場景專用角色設定 |
| | `validateCharacterProfile()` | 角色設定驗證 |

## 🎨 圖片生成模組

### 圖片核心處理
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_image_core.gs` | `handleAIImageGeneration_PassiveChain()` | 被動圖片生成鏈 |
| | `executeAsyncImageGenerationV143()` | 異步圖片生成 |
| | `generateImageStory()` | 生成配圖故事 |
| | `handleStoryContinuation()` | 故事接龍功能 |
| `modules_image_generator.gs` | `generateImageWithGemini()` | Gemini圖片生成（向後兼容） |
| `modules_image_push.gs` | `pushImageResultV143()` | 推送圖片結果 |
| | `replyWithImage()` | Reply API圖片回覆 |
| `modules_image_buttons.gs` | `handleShareStoryButton()` | 分享故事按鈕 |
| | `handleStoryParamsButton()` | 故事參數按鈕 |
| | `handleStoryContinueButton()` | 故事接龍按鈕 |
| `modules_image_utils.gs` | `shortenUrl()` | 縮短URL |
| | `validateImageUrl()` | 驗證圖片URL |

## 🔊 音頻處理模組

### 音頻核心功能
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_audio_handler.gs` | `handleAITTSRequest()` | TTS請求處理 |
| | `handleAIConversationalAudio()` | 對話音頻處理 |
| | `testAudioHandlers_debug()` | 音頻處理器測試 |
| `modules_audio_advanced.gs` | `textToSpeechWithGemini()` | Gemini TTS |
| | `callGeminiAudioDialog()` | Gemini音頻對話 |

## 📁 檔案處理模組

### 檔案管理與查詢
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_file_drive.gs` | `uploadToGoogleDrive()` | 上傳到Google Drive |
| | `recordFileToMemory()` | 記錄檔案到記憶庫 |
| `modules_file_extractor.gs` | `extractImagePrompt()` | 提取圖片提示詞 |
| | `extractTextForTTS()` | 提取TTS文字 |
| | `testContentExtraction_debug()` | 內容提取測試 |
| `modules_note_memory.gs` | `handleFileReference()` | 檔案引用處理 |
| | `findFileWithAI()` | AI檔案匹配 |
| | `findFileWithFallback()` | 備用檔案匹配 |

## 👥 群組管理模組

### 群組追蹤與查詢
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_group_tracker.gs` | `recordGroupMessage()` | 記錄群組發言 |
| | `searchUserMessages()` | 搜尋用戶發言 |
| | `handleSmartGroupQuery()` | 智能群組查詢 |
| | `getUserMappingWithCache()` | 用戶映射緩存 |

## 🤖 AI 功能模組

### 智能回應與分析
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_ai_features.gs` | `callGeminiWithSmartSearch()` | 智能搜索 Gemini 調用 |
| | `analyzeImageUploadIntent()` | 圖片上傳意圖分析 |
| | `handleAIGeneralRequest()` | AI 一般請求處理 |

## 📝 筆記與記憶模組

### 記憶系統
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_note_memory.gs` | `handleAINoteRequest()` | AI 筆記請求處理 |
| | `recordNoteToMemory()` | 記錄筆記到記憶庫 |
| | `searchMemoryWithAI()` | AI 記憶搜索 |
| | `handleFileReference()` | 檔案引用處理 |

## 📁 檔案處理模組

### 檔案管理與查詢
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_file_handler.gs` | `handleFileAnalysisRequest()` | 檔案分析請求處理 |
| | `analyzeFileContent()` | 分析檔案內容 |
| | `extractFileMetadata()` | 提取檔案元數據 |

## 💬 對話回顧模組

### 對話管理
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `modules_conversation_review.gs` | `reviewConversationHistory()` | 回顧對話歷史 |
| | `generateConversationSummary()` | 生成對話摘要 |
| | `searchConversationContext()` | 搜索對話上下文 |

## 📊 統計與報告

### 系統監控
| 檔案 | 主要函數 | 功能描述 |
|------|----------|----------|
| `core_main.gs` | `getSystemStats()` | 獲取系統統計 |
| | `systemHealthCheck()` | 系統健康檢查 |
| `core_module_manager.gs` | `runSafetyNetCheck()` | 執行安全網檢查 |
| | `validateModuleMapping()` | 驗證模組映射 |
| | `generateModuleReport()` | 生成模組狀態報告 |

## 🎛️ 功能開關映射

### 模組功能對應（v1.5.0 重構後）
| 功能名稱 | 對應模組 | 檔案前綴 |
|----------|----------|----------|
| `IMAGE_GENERATION` | 圖片生成 | `modules_image_*` |
| `TEXT_TO_SPEECH` | 音頻處理 | `modules_audio_*` |
| `NOTE_TAKING` | 筆記管理 | `modules_note_*` |
| `FILE_QUERY` | 檔案查詢 | `modules_file_*` |
| `GROUP_CHAT_TRACKING` | 群組追蹤 | `modules_group_*` |
| `LINE_WEBHOOK` | LINE 處理 | `modules_line_webhook_*` |
| `AI_PROMPTS` | AI 提示詞 | `modules_ai_prompts_*` |

## 📝 調用鏈架構（v1.5.0 重構）

### 模組化處理流程
1. **HTTP 入口**: `modules_line_webhook_core.gs` → `doPost()`
2. **事件路由**: 根據來源類型分發到對應模組
   - 群組訊息 → `modules_line_webhook_group.gs`
   - 私人訊息 → `modules_line_webhook_private.gs`
   - Postback 事件 → `modules_line_webhook_postback.gs`
   - LINE 事件 → `modules_line_webhook_events.gs`
3. **AI 處理**: `modules_line_processor_core.gs` → AI-First 架構
4. **提示詞系統**: `modules_ai_prompts_*.gs` → 智能提示詞管理
5. **功能模組**: 根據意圖路由到對應功能模組

### 重構特點
- **統一前綴命名**: 便於識別和管理
- **功能邏輯分層**: 群組/私人/事件清晰分離
- **GAS 平行執行**: 充分利用共享作用域特性
- **向後兼容**: 保留原有調用接口
- **安全網檢查**: 自動驗證模組映射和依賴關係

### 主要入口點
- `doPost()` → `routeEventToHandler()` → 路由到對應處理函數
- AI-First系統：`handleTextMessageAIFirst()`
- 提示詞系統：`callAIWithPrompt()`
- 安全網檢查：`runSafetyNetCheck()`

## 🚨 函數名稱衝突防護

### 已知函數重複問題
| 問題日期 | 衝突函數 | 影響檔案 | 解決方案 | 狀態 |
|----------|----------|----------|----------|------|
| 2025-07-17 | `doPost()` | `test_webhook.gs` vs `modules_line_webhook.gs` | 重命名為 `doPostTest()` | ✅ 已修復 |

### 實際函數清單 (v2.6.0)

#### 🌐 Webhook 與測試函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `doGet()` | `test_webhook.gs` | 測試 webhook GET 端點 | 測試 |
| `doPost()` | `modules_line_webhook.gs` | LINE Webhook 主入口 | 核心 |
| `doPostTest()` | `test_webhook.gs` | 測試 webhook POST 端點 | 測試 |
| `runTestViaWebhook()` | `test_webhook.gs` | 通過 webhook 執行測試 | 測試 |
| `執行指定測試()` | `test_webhook.gs` | 執行指定的測試函數 | 測試 |
| `simpleTest()` | `simple_test.gs` | 簡單測試函數 | 測試 |

#### 🔊 語音錯誤處理函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `displayVoiceErrorToUser()` | `modules_voice_error_display.gs` | 顯示語音錯誤給用戶 | 模組 |
| `determineVoiceErrorType()` | `modules_voice_error_display.gs` | 判斷語音錯誤類型 | 模組 |
| `handleVoiceErrorWithDisplay()` | `modules_voice_error_display.gs` | 處理語音錯誤並顯示 | 模組 |

#### 👤 用戶功能分析函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `getUserFunctionsList()` | `modules_user_functions.gs` | 獲取用戶功能列表 | 模組 |
| `analyzeUserIntentWithFunctionsList()` | `modules_user_functions.gs` | 分析用戶意圖 | 模組 |
| `buildFunctionsPrompt()` | `modules_user_functions.gs` | 構建功能列表提示詞 | 模組 |
| `parseIntentResponse()` | `modules_user_functions.gs` | 解析AI意圖回應 | 模組 |
| `calculateConfidence()` | `modules_user_functions.gs` | 計算信心度 | 模組 |
| `fallbackIntentAnalysis()` | `modules_user_functions.gs` | 備用意圖分析 | 模組 |
| `testUserFunctionsList_debug()` | `modules_user_functions.gs` | 測試功能列表系統 | 測試 |

#### 🤖 智能回應工具函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `get_conversation_context()` | `modules_smart_responder_utils.gs` | 獲取對話上下文 | 模組 |
| `get_recent_group_messages()` | `modules_smart_responder_utils.gs` | 獲取最近群組訊息 | 模組 |
| `extract_recent_topics()` | `modules_smart_responder_utils.gs` | 提取最近話題 | 模組 |
| `set_smart_responder_enabled()` | `modules_smart_responder_utils.gs` | 設定智能回應啟用狀態 | 模組 |
| `get_smart_responder_stats()` | `modules_smart_responder_utils.gs` | 獲取智能回應統計 | 模組 |
| `check_smart_responder_status()` | `modules_smart_responder_utils.gs` | 檢查智能回應狀態 | 模組 |
| `reset_smart_responder_stats()` | `modules_smart_responder_utils.gs` | 重置智能回應統計 | 模組 |

#### 🤖 智能回應核心函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `analyze_group_conversation()` | `modules_smart_responder.gs` | 分析群組對話 | 模組 |
| `checkCharacterNameTrigger()` | `modules_smart_responder.gs` | 檢查角色名稱觸發 | 模組 |
| `should_bot_respond()` | `modules_smart_responder.gs` | 判斷機器人是否應回應 | 模組 |
| `generate_smart_response()` | `modules_smart_responder.gs` | 生成智能回應 | 模組 |
| `determineGroupAtmosphere()` | `modules_smart_responder.gs` | 判斷群組氛圍 | 模組 |
| `analyze_media_response_decision()` | `modules_smart_responder.gs` | 分析媒體回應決策 | 模組 |
| `analyze_image_upload_intent()` | `modules_smart_responder.gs` | 分析圖片上傳意圖 | 模組 |
| `execute_cost_saver_media_response()` | `modules_smart_responder.gs` | 執行成本節省媒體回應 | 模組 |
| `determineShouldUseVoice()` | `modules_smart_responder.gs` | 判斷是否使用語音 | 模組 |
| `execute_smart_response()` | `modules_smart_responder.gs` | 執行智能回應 | 模組 |
| `analyzeUploadedImageContent()` | `modules_smart_responder.gs` | 分析上傳圖片內容 | 模組 |
| `shouldRespondToImageIntent()` | `modules_smart_responder.gs` | 判斷圖片是否應觸發回應 | 模組 |

#### 💰 Reply 成本節省函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `analyze_group_conversation_with_reply_cost_saver()` | `modules_reply_cost_saver.gs` | Reply成本節省分析 | 模組 |
| `getCurrentTimeRange()` | `modules_reply_cost_saver.gs` | 獲取當前時間範圍 | 模組 |
| `checkFrequencyLimit()` | `modules_reply_cost_saver.gs` | 檢查頻率限制 | 模組 |
| `check_reply_cost_saver_opportunity()` | `modules_reply_cost_saver.gs` | 檢查Reply成本節省機會 | 模組 |
| `check_basic_reply_conditions()` | `modules_reply_cost_saver.gs` | 檢查基本回覆條件 | 模組 |
| `generate_reply_cost_saver_response()` | `modules_reply_cost_saver.gs` | 生成Reply成本節省回應 | 模組 |
| `generate_image_reply_cost_saver_response()` | `modules_reply_cost_saver.gs` | 生成圖片Reply成本節省回應 | 模組 |
| `execute_reply_cost_saver_response()` | `modules_reply_cost_saver.gs` | 執行Reply成本節省回應 | 模組 |
| `getActiveCharacterProfile()` | `modules_reply_cost_saver.gs` | 獲取啟用角色設定 | 模組 |
| `generateContextualImagePrompt()` | `modules_reply_cost_saver.gs` | 生成情境化圖片提示詞 | 模組 |
| `getTimeContextDescription()` | `modules_reply_cost_saver.gs` | 獲取時段情境描述 | 模組 |
| `getSentCount()` | `modules_reply_cost_saver.gs` | 獲取發送次數 | 模組 |
| `recordSentCount()` | `modules_reply_cost_saver.gs` | 記錄發送次數 | 模組 |

#### 📋 Postback 管理函數
| 函數名稱 | 所在檔案 | 功能描述 | 類型 |
|----------|----------|----------|------|
| `getOrCreateSheet()` | `modules_postback_manager.gs` | 獲取或創建工作表 | 模組 |
| `storePostbackData()` | `modules_postback_manager.gs` | 存儲 postback 數據 | 模組 |
| `retrievePostbackData()` | `modules_postback_manager.gs` | 獲取 postback 數據 | 模組 |
| `generateUniqueId()` | `modules_postback_manager.gs` | 生成唯一ID | 模組 |
| `createSafePostbackDataV2()` | `modules_postback_manager.gs` | 創建安全的 postback 數據 | 模組 |

### 函數命名規範
1. **測試函數**: 必須使用 `_debug` 或 `_disposable` 後綴
2. **核心函數**: 避免使用通用名稱如 `doPost`, `doGet`
3. **模組函數**: 使用描述性名稱，避免與其他模組衝突
4. **工具函數**: 使用 `get`, `set`, `check`, `analyze` 等前綴

### 自動檢查機制
- 定期掃描所有 .gs 檔案中的函數定義
- 檢查是否有重複的函數名稱
- 在安全網檢查中加入函數重複檢查

---
*本文檔基於 tribe-line-bot v2.6.0 實際代碼狀況生成*
