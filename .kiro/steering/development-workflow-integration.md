# 開發流程整合 - 函數重複防護

## 🎯 目的
將函數重複防護機制整合到日常開發流程中，實現事先預防和事後檢查的完整防護。

## 🔄 開發流程整合

### 階段一：修改代碼前 (事先預防)
**執行**: `修改前檢查_debug()`
```bash
# 透過 webhook 執行
GET: ?action=test&func=修改前檢查_debug
```

或在 GAS 控制台執行：
```javascript
修改前檢查_debug()
```

**檢查項目**:
- 快速掃描現有重複函數
- 確認當前代碼狀態安全
- 提供修改建議

**結果處理**:
- ✅ 安全：可以進行代碼修改
- ⚠️ 有問題：先修復重複函數再修改

### 階段二：修改代碼中 (實時指導)
**遵循規範**: 參考 `function-naming-rules.md`
- 使用描述性函數名稱
- 避免通用名稱 (`doPost`, `main`, `process`)
- 測試函數使用 `_debug` 或 `_disposable` 後綴
- 模組函數使用適當前綴

### 階段三：修改代碼後 (事後檢查)
**執行**: `修改後檢查_debug()`
```bash
# 透過 webhook 執行
GET: ?action=test&func=修改後檢查_debug
```

或在 GAS 控制台執行：
```javascript
修改後檢查_debug()
```

**檢查項目**:
- 完整掃描所有函數
- 檢查重複函數
- 檢查禁止函數名稱
- 生成詳細報告

### 階段四：定期維護 (文檔同步)
**執行**: `生成函數清單報告_debug()`
```bash
# 透過 webhook 執行
GET: ?action=test&func=生成函數清單報告_debug
```

或在 GAS 控制台執行：
```javascript
生成函數清單報告_debug()
```

**維護任務**:
- 自動生成最新函數清單
- 更新 `函數清單.md` 文檔
- 識別需要重構的函數

## 🛡️ 自動化防護機制

### 1. 快速檢查 (日常使用)
```javascript
快速重複檢查_debug()
```
- 只檢查重複函數
- 執行速度快
- 適合頻繁使用

### 2. 完整掃描 (深度檢查)
```javascript
函數重複掃描器_debug()
```
- 檢查重複函數
- 檢查禁止函數名稱
- 生成詳細統計報告
- 適合重要修改後使用

### 3. 文檔生成 (維護工具)
```javascript
生成函數清單報告_debug()
```
- 自動生成函數清單
- 按檔案類型分組
- 標記問題函數
- 提供修復建議

## 📋 實際使用流程

### 日常開發流程
```mermaid
graph TD
    A[準備修改代碼] --> B[執行修改前檢查]
    B --> C{檢查結果}
    C -->|安全| D[進行代碼修改]
    C -->|有問題| E[修復重複函數]
    E --> B
    D --> F[執行修改後檢查]
    F --> G{檢查結果}
    G -->|通過| H[提交代碼]
    G -->|有問題| I[修復問題]
    I --> F
```

### 定期維護流程
```mermaid
graph TD
    A[每週維護] --> B[生成函數清單報告]
    B --> C[檢查報告內容]
    C --> D{發現問題?}
    D -->|是| E[修復重複/禁止函數]
    D -->|否| F[更新函數清單.md]
    E --> F
    F --> G[提交文檔更新]
```

## 🎯 Steering 整合指導

### 對 AI 助手的指導
當你需要修改代碼時，請遵循以下流程：

1. **修改前**：
   - 執行 `修改前檢查_debug()` 確認安全
   - 如有重複函數，先修復再繼續

2. **修改中**：
   - 遵循函數命名規範
   - 避免使用禁止的通用函數名
   - 測試函數放在 `_test` 目錄

3. **修改後**：
   - 執行 `修改後檢查_debug()` 驗證安全
   - 如有問題，立即修復

4. **提交前**：
   - 確保所有檢查通過
   - 更新相關文檔

### 對開發者的指導
- 養成使用檢查工具的習慣
- 定期更新函數清單文檔
- 遇到重複函數立即修復，不要拖延

## 🔧 工具使用指南

### 腳本位置
`_test/20250717_143000_function_duplicate_scanner_debug.gs`

### 主要函數
| 函數名稱 | 用途 | 使用時機 |
|----------|------|----------|
| `修改前檢查_debug()` | 快速安全檢查 | 修改代碼前 |
| `修改後檢查_debug()` | 完整驗證檢查 | 修改代碼後 |
| `函數重複掃描器_debug()` | 深度掃描分析 | 重大修改後 |
| `生成函數清單報告_debug()` | 文檔維護工具 | 定期維護 |
| `快速重複檢查_debug()` | 輕量級檢查 | 日常驗證 |

### 執行環境
- Google Apps Script 控制台
- Webhook 測試執行 (透過 test_webhook.gs)
- 瀏覽器 GET 請求測試

## 📈 效果評估

### 預期效果
- ✅ 100% 防止函數名稱衝突
- ✅ 自動化檢查減少人工錯誤
- ✅ 文檔與代碼保持同步
- ✅ 開發流程標準化

### 成功指標
- 零函數重複事件
- 文檔更新及時性 > 95%
- 開發者採用率 > 90%
- 問題發現時間 < 1 小時

## 🚨 應急處理

### 發現重複函數時
1. **立即停止相關功能**
2. **執行完整掃描確認影響範圍**
3. **按優先級修復重複函數**
4. **驗證修復效果**
5. **更新文檔記錄**

### 工具失效時
1. **使用手動檢查命令**
2. **檢查腳本檔案完整性**
3. **恢復備份版本**
4. **聯繫技術支援**

---
*此整合方案基於實際開發經驗制定，確保函數重複防護機制有效運作*