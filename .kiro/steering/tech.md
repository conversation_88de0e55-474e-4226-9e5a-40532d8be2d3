# Technology Stack & Build System

## Core Platform

**Google Apps Script (GAS)** - Primary runtime environment
- JavaScript V8 runtime
- No ES6 modules support - all .gs files share global scope
- Functions can be called directly across files without imports
- Timezone: Asia/Taipei

## Database & Storage

- **Google Sheets** - Primary database with multiple worksheets
- **Google Drive** - File storage and media handling
- **PropertiesService** - Configuration and state management

## AI & External APIs

- **Gemini AI** - Primary AI processing (2.5 Pro/Flash models)
- **LINE Messaging API** - Bot communication platform
- **Google Search API** - Smart search integration (optional)
- **YouTube Data API** - Content integration (optional)
- **Cloudinary** - Image processing (optional)

## Development Tools

- **clasp** - Google Apps Script CLI tool
- **Git** - Version control with GitHub integration
- **GitHub Actions** - Auto-deployment to GAS

## Key Constraints

- No `fetch()` - use `UrlFetchApp.fetch()` instead
- No `URLSearchParams` - use custom `parseQueryString()` function
- No modern JavaScript APIs - stick to GAS-compatible code
- Global namespace - all functions are globally accessible
- 200 version limit in GAS - never create new deployments

## Common Commands

### Development Workflow
```bash
# Test functions via webhook in GAS
# 使用 webhook 執行測試 (透過 test_webhook.gs)
# GET 請求: ?action=test&func=<functionName>

# Deploy changes (auto via GitHub)
git add .
git commit -m "commit message"
git push --force

# Health checks (run in GAS console)
systemHealthCheck()
quickSystemTest()
runCompleteSystemTest()
```

### System Management
```bash
# Initialize system
initializeSheets()
setupFeatureToggleSystem()

# Module management
autoManageModules()
runSafetyNetCheck()

# Feature testing
testFeatureToggleSystem()
testModuleManager()
```

### Configuration
- All sensitive data stored in APIKEY worksheet
- Feature toggles controlled via spreadsheet
- Module hot-swapping via feature toggle system

## File Naming Conventions

- Core system: `core_*.gs`
- Modules: `modules_<category>_<function>.gs`
- Configuration: `config_*.gs`
- Tests: `_test/*_debug.gs` or `_test/*_disposable.gs`

## Deployment

- **Entry Point**: `doPost()` function in `modules_line_webhook.gs`
- **Web App**: Deployed with public access, executes as deploying user
- **Auto-deployment**: GitHub pushes trigger GAS updates
- **Never use clasp push** - only use Git workflow