# Development Principles & Guidelines

## 🎯 Guidelines（同步 augment's User Guidelines, cloude desktop's personal preferences, gemini's saved info）
- You are a coding master. You can help me by answering my questions. You can also ask me questions.

## 🎯 共通原則（零容忍政策）
- 你要讀完此檔不能只看前50行，並且要去讀取 _docs/"專案指南"、"重構指南"。
- **最小修改，不要過度工程化 - 簡單能用的代碼不要隨意大幅修改。**
- 我的OS為 windows 11，沒有目錄權限問題。
- 所有敏感資訊 (金鑰、ID) 保留頭尾少量原文讓用戶能理解其大致格式，中間用佔位符（英文字母x）替換同樣長度的字串符，Commit 說明中也勿包含。
- 無論我用何種語言，你都用繁體中文回覆。
- 🎯修改前先從配置文件的角度思考問題，完整檢查程式流程，確認資料結構，調用鏈，數據流。
- 🎯修改時不要寫新舊功能兼容代碼，不要改腳本、函數、變數名稱，尤其禁止把名稱加上版本號如"V2"或後綴如"_fix"。
- 🎯修改完畢後要清理過程中產生的無用代碼。
- 當我們參考舊代碼寫新代碼的時候，確保命名跨專案一致性，避免不必要的改變。
- 錯誤處理必須統一規劃，捕捉所有預期外的錯誤 (exception)，可以實作自動重試 (retry) 但禁止實作功能降級 (fallback)跟備用流程，但凡出錯就顯示技術細節要求用戶回報給開發團隊。
- 當你在聊天窗口顯示函數時，要先顯示這函數所在的文件。
- 真假邏輯要可以處理各種情況：🎯 字符串 'TRUE'，🎯 布爾值 true，🎯 帶空白字符的 ' TRUE '，🎯 小寫的 'true'，🎯 混合大小寫的 'True'

## 🎯 Core Development Philosophy

### 1. **不要過度工程化 (Don't Over-Engineer)**
- **簡單能用的代碼不要隨意大幅修改**
- 如果現有功能正常運作，優先考慮小幅修復而非重構
- 避免為了"完美"而破壞已經穩定的功能
- 記住：Working code > Perfect code

### 2. **漸進式改進 (Incremental Improvement)**
- 優先修復真正的問題，而非假想的問題
- 每次修改都要有明確的目標和理由
- 保持向後兼容性，支援多種可能的格式
- 測試驅動：先確認問題存在，再進行修復

### 3. **實際環境優先 (Production-First Mindset)**
- 測試成功 ≠ 生產環境正常
- 在實際 LINE 環境中驗證功能，不只是單元測試
- 考慮不同的調用路徑和使用場景
- 重視用戶反饋勝過理論完美

## 🔧 API 開發原則

### 1. **API 格式靈活處理**
- 不同 API 端點可能有不同回應格式
- 支援多種可能的數據結構格式
- 不要假設 API 格式永遠一致
- 加入詳細的調試日誌以便問題排查

### 2. **錯誤處理完整性**
- 所有錯誤返回都要包含必要字段
- 避免 "unknown" 或 "undefined" 顯示給用戶
- 提供有意義的錯誤訊息和技術詳情
- 記錄足夠的上下文信息

### 3. **向後兼容性**
- 新功能不應破壞現有功能
- 支援舊格式的同時引入新格式
- 漸進式遷移而非一次性替換

## 🧪 測試與驗證原則

### 1. **多層次測試**
- 單元測試：驗證函數邏輯
- 集成測試：驗證模組間協作
- 實際環境測試：在 LINE 中驗證用戶體驗

### 2. **調試信息策略**
- 加入詳細但不冗餘的日誌
- 記錄關鍵決策點和數據結構
- 在錯誤情況下輸出完整上下文
- 使用結構化日誌便於分析

## 🚨 修復策略

### 1. **問題定位**
- 先理解問題的真正根源
- 區分症狀和原因
- 檢查是否為環境差異導致

### 2. **修復範圍**
- 最小化修改範圍
- 優先修復核心問題
- 避免連帶修改無關功能

### 3. **驗證策略**
- 修復後在實際環境中驗證
- 確認沒有引入新問題
- 檢查相關功能是否受影響

## 🔄 Git 原則
- 每次改完代碼，如果需要測試, 你總結作為 commit 的說明並 git push --force 到 github，顯示 commit hash 給我看，我已經設定 github 自動推動到 GAS, 絕對不要用 clasp 推送檔案到 google apps scripts。

## ⚙️ Clasp 原則
- 使用 C:\Users\<USER>\.clasprc.json 即可登入GAS執行.gs來執行測試。注意 GAS 不可創建新的部署ID，**只需更新 Head 版本，絕不建立新版本或新部署**，避免觸及GAS 200 個版本的上限。

## 📝 回答原則：使用 Markdown 與 mermaid 回答（相容 notion 格式）
### 條列清單：
- 使用「#, ##」。大綱從heading 1（#）開始，禁用 heading 3（###），不要有空行。
### code block：
- 使用 Notion 友善的程式碼區塊。
### 不要使用：
- quote。
### 使用 mermaid 時
- 以主支與分支展示詳細流程圖，語法不得高於 mermaid v9.1.7，style fill 不要用淺色方塊加淺色字，寬度不超過三個 participant。避免錯誤的 `mermaid` 語法，例如「Unsupported markdown: list」。Mermaid 不支援在圖表語法行內直接加入註解（像 ;~ 或 //），這會導致語法錯誤，請把註解拿掉，或移到圖表區塊外面。

## 🌐 HTML 原則
- 不要使用滑鼠HOVER網頁元件時, 該元件會動一下的這個效果，燈泡等其他效果要保留。

## 📄 批次檔原則
- 批次檔要求完全不含中文，使用標準批處理命令，避免了使用標籤和goto命令，路徑必須使用雙反斜線 (\\)，禁止混用 / 和 \。修改後的檔案要求直接使用原檔名。

## 🔧 GAS 原則
- Google Apps Script 在同一個項目中的所有 .gs 文件共享全局作用域，函數可以直接跨文件調用，不需要任何導入機制。
- GAS 環境不支援 URLSearchParams、fetch()、ES6 模組等現代 JavaScript API。請只使用 GAS 兼容的代碼，如果需要解析 URL 參數，請使用我們已有的 parseQueryString() 函數。
- 除非有寫配套的選單介面，否則就不應該在測試函數中加入了不應存在的 UI 彈窗指令，這是你一再犯的錯誤，Exception: Cannot call SpreadsheetApp.getUi() from this context.。
- ai-first，把複雜邏輯交給AI提示詞系統處理，少用 2010 年寫 BOT 的關鍵字、正則等代碼邏輯。
- 統一並唯一的版本號寫在/core_main.gs
- 缺了工作表或工作表缺了欄位應該要自動創建，而且創建時不可以清空舊資料。
- 郵件相關代碼需避免所有 ★ 【】📊 等特殊符號，以免產生亂碼問題。
- 腳本的第一行必須備註檔名，注意GAS只支持JavaScript註釋

## 🧪 測試的原則
- 測試函數不要寫在腳本中，一律寫在資料夾 /_test。寄發測試郵件的標題要包含"測試 - "，寄發給管理員的郵件的標題要包含"致管理員 - "。
- 測試函數一律部分使用繁體中文名稱。如果該測試函數將來還會使用，名稱就以原函數名加上後綴"_debug"，; 如果是一次性的測試或修復函數, 就加上後綴"_disposable"。
- 測試函數要集中在代碼頂部，並在備註中說明其功能。
- 自動化部署: 修改代碼後如需測試，你要以總結作為 commit 的說明，然後 git push --force
- 自動化測試: 如果你有寫測試，你要使用 webhook 執行測試 (透過 test_webhook.gs)。測試失敗你要繼續改代碼，成功則刪除不再用的腳本。
- 如果你要叫我測試，你顯示測試函數時，必須加上該函數所在的腳本名稱，例如：腳本：XXX.gs。測試函數：XXX完整測試_debug();XXX測試_disposable()。

## 📋 修改代碼前準備
- 修改代碼前要先檢查行數，如果超過700行就要考慮拆分或重構。🎯每次寫入文件前，你要先顯示文件檔名，🎯改完後要列表本次修改過的檔案。
- 你先使用code-index，了解代碼結構，不要臆測。🎯新增全局變數、函數或腳本前先確認存在性。🎯只使用標準空格，不要使用NBSP (Non-Breaking Space)。🎯在改代碼前, 如果AI不是Kiro，你要先用個簡單表格加上emoj✅或❌i說明：認不認同我這樣改, 以及復不複雜，難度高不高，有沒有明顯更好的方法(沒有的話這點就留空)。如果AI是Kiro，不要使用markdown表格格式，改用bullet points、條列清單或結構化文字來呈現對比資訊。

## 🤖 Claude Code (cc) 使用指南
- 🎯claude code 簡稱CC，CC可以處理很長的上下文，所以你優先讓 CC 深度分析 🎯執行與監控：你給CC指令他開始執行之後，你必須等待當前指令完全執行結束（無論成功或失敗）後，才能下達新指令，有時複雜任務要等幾分鐘是正常的，你在考慮強制終止長時間運行的任務前，應先檢查是否有日誌 (log) 或部分輸出，以判斷其為「仍在處理」或「完全無響應」。
- 🎯如果 CC 因用量限制中斷，你應根據現有上下文和任務目標，接手完成剩餘的程式碼修改工作。🎯使用文件輸入法避免引號轉義問題：`claude --print < prompt_file.txt`🎯使用 Heredoc 語法處理複雜指令：`claude --print << 'PROMPT' ... PROMPT`🎯非互動模式適合自動化：`echo "指令" | claude --print`🎯測試時用CC要避免長時間運行浪費 tokens，正式修改代碼不受此限。

## 📚 經驗教訓記錄

### TTS API 修復經驗 (2025-07-16)
- **問題**：過度修改格式檢查邏輯導致功能失效
- **教訓**：簡單能用的代碼不要隨意大幅修改
- **解決**：支援多種 API 回應格式，完善錯誤返回值
- **參考**：[Notion 記錄](https://www.notion.so/notion_for_AI-22de1837c7ce8085bc6dddb91b61e65f?p=232e1837c7ce80f1a7dde1676261997e&pm=s)

## 🎯 決策框架

### 修改代碼前問自己：
1. **為什麼要修改？** - 有明確的問題需要解決嗎？
2. **風險評估** - 修改可能影響哪些功能？
3. **測試計劃** - 如何驗證修改是否成功？
4. **回滾計劃** - 如果出問題如何快速恢復？

### 修改代碼時遵循：
1. **最小修改原則** - 只改必要的部分
2. **保持兼容性** - 支援舊格式
3. **加強日誌** - 便於問題排查
4. **完整測試** - 實際環境驗證

---

*這些原則基於實際開發經驗總結，會持續更新和完善。*