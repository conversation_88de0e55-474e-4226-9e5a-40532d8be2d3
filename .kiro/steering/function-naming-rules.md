# 函數命名規範與重複防護

## 🎯 目的
防止 Google Apps Script 中函數名稱衝突導致的覆蓋問題，確保系統穩定性。

## 🚨 已知問題案例
**2025-07-17 活動日誌無反應問題**:
- **問題**: `test_webhook.gs` 中的 `doPost()` 覆蓋了 `modules_line_webhook.gs` 中的真正 LINE webhook 處理函數
- **影響**: 導致活動日誌完全無法記錄，系統無法追蹤用戶互動
- **解決**: 將測試函數重命名為 `doPostTest()`
- **教訓**: GAS 全域作用域特性使得同名函數會相互覆蓋

## 📋 函數命名規範

### 1. 核心系統函數
**規則**: 使用描述性名稱，避免通用名稱
- ✅ 正確: `handleLineWebhook()`, `processUserMessage()`
- ❌ 錯誤: `doPost()`, `main()`, `process()`

### 2. 測試函數
**規則**: 必須使用明確的測試標識
- ✅ 正確: `testLogActivity_debug()`, `validateSystem_disposable()`
- ❌ 錯誤: `doPost()`, `test()`, `check()`

**測試函數後綴**:
- `_debug`: 持續性測試，可重複執行
- `_disposable`: 一次性測試，完成後可刪除
- `Test`: 測試專用函數前綴 (如 `doPostTest()`)

### 3. 模組函數
**規則**: 使用模組前綴或描述性動詞
- ✅ 正確: `analyze_group_conversation()`, `get_conversation_context()`
- ❌ 錯誤: `process()`, `handle()`, `run()`

### 4. 工具函數
**規則**: 使用標準動詞前綴
- `get*()`: 獲取數據
- `set*()`: 設定數據
- `check*()`: 檢查狀態
- `analyze*()`: 分析處理
- `generate*()`: 生成內容
- `execute*()`: 執行操作

## 🛡️ 防護機制

### 自動檢查規則
1. **禁止的通用函數名**:
   - `doPost()` (除了主要的 webhook 處理函數)
   - `doGet()` (除了主要的 GET 處理函數)
   - `main()`
   - `init()`
   - `run()`
   - `process()`
   - `handle()` (單獨使用)

2. **測試函數檢查**:
   - 所有測試相關函數必須包含 `test`, `debug`, `disposable` 關鍵字
   - 測試函數不得使用核心系統函數名稱

3. **模組函數檢查**:
   - 模組間不得有相同的函數名稱
   - 使用描述性名稱避免衝突

### 實施策略
1. **代碼審查**: 新增函數前檢查是否與現有函數重複
2. **自動化掃描**: 定期掃描所有 .gs 檔案檢查重複函數
3. **安全網檢查**: 在系統檢查中加入函數重複檢查
4. **文檔維護**: 更新 `函數清單.md` 記錄所有函數

## 🔧 檢查工具

### 自動化檢查腳本
**主要工具**: `_test/20250717_143000_function_duplicate_scanner_debug.gs`

**可用函數**:
- `函數重複掃描器_debug()` - 完整掃描和報告
- `快速重複檢查_debug()` - 快速檢查重複函數
- `生成函數清單報告_debug()` - 自動更新函數清單

### 整合到開發流程
1. **修改代碼前**: 執行 `快速重複檢查_debug()` 確認無衝突
2. **修改代碼後**: 執行 `函數重複掃描器_debug()` 完整檢查
3. **定期維護**: 執行 `生成函數清單報告_debug()` 更新文檔

### 手動檢查命令 (備用)
```bash
# 搜尋所有函數定義
grep -r "^function " *.gs

# 檢查特定函數是否重複
grep -r "function doPost" *.gs
```

## 📝 修復指南

### 發現重複函數時的處理步驟
1. **立即識別影響**: 確認哪個函數被覆蓋
2. **評估風險**: 判斷對系統功能的影響程度
3. **重命名衝突函數**: 
   - 測試函數: 加上 `Test` 前綴或 `_debug` 後綴
   - 工具函數: 使用更具體的描述性名稱
4. **驗證修復**: 確認原有功能恢復正常
5. **更新文檔**: 在 `函數清單.md` 中記錄修復

### 重命名建議
| 原函數名 | 建議重命名 | 理由 |
|----------|------------|------|
| `doPost()` (測試) | `doPostTest()` | 避免覆蓋主要 webhook |
| `main()` | `executeMainLogic()` | 更具描述性 |
| `process()` | `processUserInput()` | 明確處理對象 |
| `handle()` | `handleSpecificEvent()` | 指定處理類型 |

## 🎯 最佳實踐

1. **函數命名**: 使用動詞+名詞的組合，清楚描述功能
2. **避免縮寫**: 使用完整單詞而非縮寫
3. **一致性**: 同類功能使用相同的命名模式
4. **可讀性**: 函數名稱應該能讓其他開發者立即理解功能

## 🔄 定期維護

- **每月檢查**: 掃描新增函數是否有重複
- **版本更新**: 每次重大更新後檢查函數映射
- **文檔同步**: 保持 `函數清單.md` 與實際代碼同步

---
*此規範基於 2025-07-17 活動日誌修復經驗制定*