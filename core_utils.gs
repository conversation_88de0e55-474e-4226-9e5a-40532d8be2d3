/*
 * 檔案: core_utils.gs
 * 分類: core
 * 功能開關: -
 * 描述: 通用工具函數和配置管理
 * 依賴: [config_core.gs]
 * 最後更新: 2025-07-14 - 修復Native Audio Dialog API調用問題，添加智能降級策略
 */

// Utils.gs
// 提供全專案使用的配置管理和基礎工具函數

// 🆕 v1.5.1 動態配置讀取函數 - 根據標題名稱動態讀取配置值
// 🔧 v2.6.3 支援多組 Gemini API Key 輪換機制
/**
 * 根據設定工作表中的標題名稱，動態獲取對應的設定值。
 * 支援垂直結構（APIKEY工作表：A欄標題，B欄值）和水平結構。
 * @param {string} key - 要查找的設定項標題名稱 (例如 "lineChannelAccessToken")。
 * @param {string} [sheetName='APIKEY'] - 設定所在的工作表名稱，預設為 'APIKEY'。
 * @returns {string|null} 找到的設定值，如果沒找到則返回 null。
 */
function getConfigValue(key, sheetName = 'APIKEY') {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      console.error(`找不到工作表: ${sheetName}`);
      return null;
    }

    // 🔧 v1.5.1 修復：支援 APIKEY 工作表的垂直結構
    if (sheetName === 'APIKEY') {
      // 建立變數名到實際標題的對應表
      const keyMappings = {
        'lineChannelAccessToken': 'LINE Channel Access Token',
        'lineChannelSecret': 'LINE Channel Secret',
        'geminiApiKey': 'Gemini API Key',
        'geminiApiKeyRotationIndex': 'Gemini API Key當前輪換索引',
        'geminiApiKeyLastUpdateTime': 'Gemini API Key最後更新時間',
        'youtubeApiKey': 'YouTube Data API Key',
        'threadsUserId': 'Threads User ID',
        'threadsAccessToken': 'Threads Access Token',
        'cloudinaryCloudName': 'Cloudinary Cloud Name',
        'cloudinaryApiKey': 'Cloudinary API Key',
        'cloudinaryApiSecret': 'Cloudinary API Secret',
        'folderId': 'Google Drive Folder ID',
        'googleSearchApiKey': 'Google Search API Key',
        'googleSearchEngineId': 'Google Search Engine ID',
        'generalModel': '一般功能模型',
        'visionModel': '圖片分析模型',
        'ttsModel': '語音合成模型',
        'audioDialogModel': '對話音頻模型',
        'embeddingModel': '嵌入向量模型',
        'fastModel': '高效率模型',
        'imageGenModel': '圖片生成模型',
        'gcpProjectId': 'GCP Project ID',
        'gcpRegion': 'GCP Region',
        'footer': '系統頁尾顯示',
        'SCRIPT_VERSION': '腳本版本',
        'fixed_seed': '角色設定',
        // 🎭 角色一致性配置映射
        'fixedSeed': '固定SEED',
        'basePrompt': '基礎圖像提示詞'
      };

      // 獲取實際要搜尋的標題
      let actualTitle = keyMappings[key];

      // 🎛️ 支援功能開關：如果是功能開關，直接使用原始鍵名
      if (!actualTitle && key.startsWith('功能開關_')) {
        actualTitle = key;
      }

      if (!actualTitle) {
        console.error(`未定義的配置項: ${key}`);
        return null;
      }

      // APIKEY 工作表使用垂直結構：A欄是標題，B欄是值
      const lastRow = sheet.getLastRow();
      if (lastRow < 2) return null;

      const data = sheet.getRange(1, 1, lastRow, 2).getValues();

      for (let i = 0; i < data.length; i++) {
        const title = data[i][0]; // A欄：標題
        const value = data[i][1]; // B欄：值

        if (title && title.toString() === actualTitle) {
          return value || '';
        }
      }

      console.error(`在工作表 "${sheetName}" 中找不到設定項: ${actualTitle}`);
      return null;

    } else {
      // 其他工作表使用水平結構：第1行是標題，第2行是值
      const data = sheet.getRange(1, 1, 2, sheet.getLastColumn()).getValues();
      const headers = data[0]; // 第一行是標題
      const values = data[1];  // 第二行是值

      const columnIndex = headers.indexOf(key);

      if (columnIndex !== -1) {
        return values[columnIndex] || '';
      } else {
        console.error(`在工作表 "${sheetName}" 中找不到設定項: ${key}`);
        return null;
      }
    }
  } catch (e) {
    console.error(`讀取設定值 "${key}" 時發生錯誤: ${e}`);
    return null;
  }
}

// 🔧 v2.6.3 多組 Gemini API Key 管理系統
/**
 * 解析多組 API Key（支援逗號分隔或換行分隔）
 * @param {string} keyString - API Key 字串
 * @returns {Array<string>} API Key 陣列
 */
function parseMultipleApiKeys(keyString) {
  if (!keyString) return [];

  // 支援 Ctrl+Enter 換行分隔（\r\n）或逗號分隔
  const keys = keyString.split(/[\r\n,]+/)
    .map(key => key.trim())
    .filter(key => key.length > 0);

  console.log(`🔑 解析到 ${keys.length} 組 API Key`);
  return keys;
}

/**
 * 獲取當前可用的 Gemini API Key
 * @returns {string} 當前可用的 API Key
 */
function getCurrentGeminiApiKey() {
  try {
    const keyString = getConfigValue('geminiApiKey');
    const keys = parseMultipleApiKeys(keyString);

    if (keys.length === 0) {
      throw new Error('未設定 Gemini API Key');
    }

    if (keys.length === 1) {
      return keys[0]; // 單組 Key 直接返回
    }

    // 多組 Key 使用輪換機制
    const currentIndex = getApiKeyRotationIndex();
    const selectedKey = keys[currentIndex % keys.length];

    console.log(`🔄 使用第 ${(currentIndex % keys.length) + 1} 組 API Key (共 ${keys.length} 組)`);
    return selectedKey;

  } catch (error) {
    console.error('❌ 獲取 Gemini API Key 失敗:', error);
    throw error;
  }
}

/**
 * 獲取 API Key 輪換索引（從APIKEY工作表讀取）
 * @returns {number} 當前輪換索引
 */
function getApiKeyRotationIndex() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');

    if (!apikeySheet) {
      console.error('❌ APIKEY 工作表不存在');
      return 0;
    }

    // 檢查是否已有輪換索引行
    const indexTitle = apikeySheet.getRange('A5').getValue();
    if (indexTitle !== 'Gemini API Key當前輪換索引') {
      // 初始化輪換索引行
      apikeySheet.getRange('A5').setValue('Gemini API Key當前輪換索引');
      apikeySheet.getRange('B5').setValue(0);
      apikeySheet.getRange('A6').setValue('Gemini API Key最後更新時間');
      apikeySheet.getRange('B6').setValue(new Date());
      console.log('✅ 初始化 APIKEY 工作表輪換狀態');
    }

    const currentIndex = apikeySheet.getRange('B5').getValue() || 0;
    return parseInt(currentIndex);

  } catch (error) {
    console.error('❌ 獲取輪換索引失敗:', error);
    return 0; // 默認返回 0
  }
}

/**
 * 更新 API Key 輪換索引（429 錯誤時調用）
 */
function rotateToNextApiKey() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');

    if (!apikeySheet) {
      console.error('❌ APIKEY 工作表不存在');
      return;
    }

    const currentIndex = getApiKeyRotationIndex();
    const nextIndex = currentIndex + 1;

    // 更新APIKEY工作表中的輪換狀態
    apikeySheet.getRange('B5').setValue(nextIndex);
    apikeySheet.getRange('B6').setValue(new Date());

    console.log(`🔄 API Key 輪換：${currentIndex} → ${nextIndex}`);

    // 記錄輪換日誌
    logActivity('System', 'API Key 輪換', 'Success', 'api', 'rotateToNextApiKey',
                `從索引 ${currentIndex} 輪換到 ${nextIndex}`);

  } catch (error) {
    console.error('❌ API Key 輪換失敗:', error);
  }
}

// 1. 🚀 Configuration - 從 APIKEY 工作表讀取設定（支援所有功能模型）v1.5.1 動態版本
function getConfig() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
  if (!sheet) {
    throw new Error('找不到 APIKEY 工作表，請先執行 initializeSheets()');
  }

  // 🆕 v1.5.1：使用動態配置讀取，向後兼容硬編碼方式
  // 🔧 v2.6.3：支援多組 Gemini API Key 輪換，欄位位置已調整
  return {
    lineChannelAccessToken: getConfigValue('lineChannelAccessToken') || sheet.getRange('B1').getValue() || '',
    lineChannelSecret: getConfigValue('lineChannelSecret') || sheet.getRange('B2').getValue() || '',
    geminiApiKey: getCurrentGeminiApiKey(), // 🔧 使用輪換機制
    geminiApiKeyRotationIndex: getConfigValue('geminiApiKeyRotationIndex') || sheet.getRange('B5').getValue() || 0,
    geminiApiKeyLastUpdateTime: getConfigValue('geminiApiKeyLastUpdateTime') || sheet.getRange('B6').getValue() || '',
    youtubeApiKey: getConfigValue('youtubeApiKey') || sheet.getRange('B7').getValue() || '', // 從B5移至B7
    threadsUserId: getConfigValue('threadsUserId') || sheet.getRange('B8').getValue() || '', // 從B6移至B8
    threadsAccessToken: getConfigValue('threadsAccessToken') || sheet.getRange('B9').getValue() || '',
    cloudinaryCloudName: getConfigValue('cloudinaryCloudName') || sheet.getRange('B10').getValue() || '',
    cloudinaryApiKey: getConfigValue('cloudinaryApiKey') || sheet.getRange('B11').getValue() || '',
    cloudinaryApiSecret: getConfigValue('cloudinaryApiSecret') || sheet.getRange('B12').getValue() || '',
    folderId: getConfigValue('folderId') || sheet.getRange('B13').getValue() || '',
    googleSearchApiKey: getConfigValue('googleSearchApiKey') || sheet.getRange('B14').getValue() || '',
    googleSearchEngineId: getConfigValue('googleSearchEngineId') || sheet.getRange('B15').getValue() || '',
    generalModel: getConfigValue('generalModel') || sheet.getRange('B16').getValue() || 'gemini-2.5-flash',
    visionModel: getConfigValue('visionModel') || sheet.getRange('B17').getValue() || 'gemini-2.5-pro',
    ttsModel: getConfigValue('ttsModel') || sheet.getRange('B18').getValue() || 'gemini-2.5-flash-preview-tts',
    audioDialogModel: getConfigValue('audioDialogModel') || sheet.getRange('B19').getValue() || 'gemini-2.5-flash-exp-native-audio-thinking-dialog',
    embeddingModel: getConfigValue('embeddingModel') || sheet.getRange('B20').getValue() || 'gemini-embedding-exp',
    fastModel: getConfigValue('fastModel') || sheet.getRange('B21').getValue() || 'gemini-2.5-flash-lite-preview-06-17',
    imageGenModel: getConfigValue('imageGenModel') || sheet.getRange('B22').getValue() || 'gemini-2.0-flash-preview-image-generation',
    gcpProjectId: getConfigValue('gcpProjectId') || sheet.getRange('B23').getValue() || '',
    gcpRegion: getConfigValue('gcpRegion') || sheet.getRange('B24').getValue() || 'us-central1',
    footer: getConfigValue('footer') || sheet.getRange('B25').getValue() || '❤️ 台灣智能經貿協會'
  };
}

// 2. 🧠 智能模型選擇器 - 根據任務自動選擇最佳模型
function getModelForTask(taskType, userPreference = null) {
  const config = getConfig();
  
  // 如果用戶指定了偏好模型，先驗證是否適合
  if (userPreference) {
    const validation = validateModelForFunction(userPreference, taskType);
    if (validation.isValid) {
      console.log(`✅ 使用用戶指定模型: ${userPreference} (任務: ${taskType})`);
      return userPreference;
    } else {
      console.warn(`⚠️ 用戶指定模型不適合任務 ${taskType}:`, validation.warnings);
    }
  }
  
  // 根據任務類型選擇模型
  switch (taskType) {
    case 'text_to_speech':
    case 'tts':
      return config.ttsModel;
      
    case 'conversational_audio':
    case 'audio_dialog':
    case 'voice_chat':
      return config.audioDialogModel;
      
    case 'image_generation':
    case 'image_creation':
    case 'ai_art':
      return config.imageGenModel;
      
    case 'semantic_search':
    case 'document_search':
    case 'embedding':
      return config.embeddingModel;
      
    case 'vision_analysis':
    case 'image_analysis':
    case 'visual_understanding':
      return config.visionModel;
      
    case 'fast_processing':
    case 'batch_processing':
    case 'cost_effective':
      return config.fastModel;
      
    case 'general':
    case 'text_processing':
    case 'chat':
    default:
      return config.generalModel;
  }
}

// 3. [核心] 🚀 增強版統一 Gemini API 呼叫函數 - 支援智能模型選擇
function callGemini(prompt, modelType = 'general', imageBlob = null, taskHint = null) {
  const config = getConfig();
  if (!config.geminiApiKey) throw new Error('Gemini API Key 未設定');

  // 🧠 智能選擇模型：優先使用任務提示，然後使用模型類型
  let modelName;
  if (taskHint) {
    modelName = getModelForTask(taskHint);
    console.log(`🎯 根據任務提示 "${taskHint}" 選擇模型: ${modelName}`);
  } else {
    modelName = modelType === 'vision' ? config.visionModel : config.generalModel;
    console.log(`📋 根據模型類型 "${modelType}" 選擇模型: ${modelName}`);
  }
  
  // 🔍 模型功能驗證
  if (imageBlob && !modelSupports(modelName, 'image')) {
    console.warn(`⚠️ 模型 ${modelName} 可能不支援圖片分析，嘗試使用視覺模型`);
    modelName = config.visionModel;
  }
  
  // 🚀 動態獲取模型對應的 API 版本
  const apiVersion = getModelApiVersion(modelName);
  const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;

  let parts = [{ text: prompt }];
  if (imageBlob) {
    parts.push({
      inline_data: {
        mime_type: imageBlob.getContentType(),
        data: Utilities.base64Encode(imageBlob.getBytes())
      }
    });
  }

  const payload = JSON.stringify({ contents: [{ parts: parts }] });
  const options = { 
    method: 'POST', 
    headers: { 'Content-Type': 'application/json' }, 
    payload: payload, 
    muteHttpExceptions: true 
  };

  try {
    // 🕐 開始計時
    const startTime = Date.now();

    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    // 🕐 計算回應時間
    const responseTime = Date.now() - startTime;

    if (responseCode !== 200) {
      console.error(`Gemini API 錯誤 (${modelName}, ${apiVersion}) - Code: ${responseCode}, Response: ${responseText}`);

      // 🔧 v2.6.3 處理 429 配額超限錯誤
      if (responseCode === 429) {
        console.log('🚨 檢測到 429 配額超限錯誤，嘗試輪換 API Key...');

        try {
          // 解析錯誤詳情
          const errorData = JSON.parse(responseText);
          const isQuotaExceeded = errorData.error &&
            (errorData.error.message.includes('quota') ||
             errorData.error.message.includes('RESOURCE_EXHAUSTED'));

          if (isQuotaExceeded) {
            // 輪換到下一組 API Key
            rotateToNextApiKey();

            // 使用新的 API Key 重試一次
            const newConfig = getConfig();
            const retryUrl = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${newConfig.geminiApiKey}`;

            console.log('🔄 使用新 API Key 重試...');
            const retryResponse = UrlFetchApp.fetch(retryUrl, options);
            const retryCode = retryResponse.getResponseCode();
            const retryText = retryResponse.getContentText();

            if (retryCode === 200) {
              console.log('✅ API Key 輪換成功，重試調用成功');
              const retryResult = JSON.parse(retryText);
              if (retryResult.candidates && retryResult.candidates[0].content && retryResult.candidates[0].content.parts) {
                logActivity('System', 'Gemini API 調用(輪換成功)', 'Success', 'api', 'callGeminiAPI',
                           `模型: ${modelName}, API版本: ${apiVersion}, 任務: ${taskHint || modelType}`, responseTime);
                return retryResult.candidates[0].content.parts[0].text.trim();
              }
            } else {
              console.log(`⚠️ 輪換後仍失敗: ${retryCode}`);
              // 如果輪換後仍失敗，拋出用戶友好的錯誤訊息
              throw new Error(`🚨 所有 API Key 配額已用完，請聯繫工程部檢查配額狀態

📊 錯誤詳情：
- 錯誤代碼：429 (配額超限)
- 模型：${modelName}
- 時間：${new Date().toLocaleString('zh-TW')}

💡 建議：
1. 檢查 Google AI Studio 配額使用情況
2. 考慮升級到付費方案
3. 等待配額重置（通常為每日重置）

🔗 更多資訊：https://ai.google.dev/gemini-api/docs/rate-limits`);
            }
          }
        } catch (parseError) {
          console.error('❌ 解析 429 錯誤失敗:', parseError);
        }
      }

      // 如果是 v1beta 失敗，嘗試 v1 版本
      if (apiVersion === 'v1beta') {
        console.log(`嘗試使用 v1 版本重新調用 ${modelName}...`);
        const fallbackUrl = `https://generativelanguage.googleapis.com/v1/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
        
        const fallbackResponse = UrlFetchApp.fetch(fallbackUrl, options);
        const fallbackCode = fallbackResponse.getResponseCode();
        const fallbackText = fallbackResponse.getContentText();
        
        if (fallbackCode === 200) {
          console.log(`✅ v1 版本調用成功`);
          const fallbackResult = JSON.parse(fallbackText);
          if (fallbackResult.candidates && fallbackResult.candidates[0].content && fallbackResult.candidates[0].content.parts) {
            // 🕐 記錄成功的API調用（包含回應時間）
            logActivity('System', 'Gemini API 調用(v1備用)', 'Success', 'api', 'callGeminiAPI', `模型: ${modelName}, API版本: v1(備用), 任務: ${taskHint || modelType}`, responseTime);
            return fallbackResult.candidates[0].content.parts[0].text.trim();
          }
        }
      }
      
      throw new Error(`Gemini API 呼叫失敗 (${modelName}, ${apiVersion}): ${responseText}`);
    }

    const result = JSON.parse(responseText);
    if (!result.candidates || !result.candidates[0].content || !result.candidates[0].content.parts) {
      throw new Error('Gemini API 回應格式不正確');
    }

    // 🕐 記錄成功的API調用（包含回應時間）
    logActivity('System', 'Gemini API 調用', 'Success', 'api', 'callGeminiAPI', `模型: ${modelName}, API版本: ${apiVersion}, 任務: ${taskHint || modelType}`, responseTime);

    return result.candidates[0].content.parts[0].text.trim();
    
  } catch (error) {
    console.error('Gemini API 調用錯誤:', error);
    // 🕐 記錄失敗的API調用（如果有計算時間的話也包含）
    const errorResponseTime = typeof responseTime !== 'undefined' ? responseTime : null;
    logActivity('System', 'Gemini API 錯誤', 'Failure', 'api', 'callGeminiAPI', `模型: ${modelName}, 錯誤: ${error.message}`, errorResponseTime);
    throw error;
  }
}

// 4. 🎯 AI-First 專用：根據意圖智能選擇模型
function callGeminiWithIntent(prompt, userIntent, imageBlob = null) {
  let taskHint = null;
  
  // 根據 AI 分析的意圖映射到任務類型
  switch (userIntent.primary_intent) {
    case 'text_to_speech':
      taskHint = 'tts';
      break;
    case 'image_generation':
      taskHint = 'image_generation';
      break;
    case 'file_query':
    case 'conversation_review':
      taskHint = 'semantic_search';
      break;
    case 'vision_analysis':
      taskHint = 'vision_analysis';
      break;
    case 'group_member_query':
      taskHint = 'fast_processing'; // 群組查詢通常需要快速處理
      break;
    default:
      taskHint = 'general';
  }
  
  console.log(`🎯 根據意圖 "${userIntent.primary_intent}" 映射任務類型: ${taskHint}`);
  
  return callGemini(prompt, imageBlob ? 'vision' : 'general', imageBlob, taskHint);
}

// 5. 🔊 語音合成專用：TTS 文字轉語音（鸚鵡模式）
function callGeminiTTS(text, voiceConfig = {}) {
  try {
    const config = getConfig();
    
    // 🔧 使用輪換機制獲取 API Key
    let apiKey;
    try {
      apiKey = getCurrentGeminiApiKey();
    } catch (error) {
      console.error('獲取 Gemini API Key 失敗:', error);
      return {
        success: false,
        error: '未設定 Gemini API Key',
        modelUsed: config?.ttsModel || 'unknown', // 🔧 修復：使用實際模型名稱
        isPlayable: false
      };
    }

    const modelName = config.ttsModel;
    console.log(`🔊 鸚鵡模式 - 使用 TTS 模型: ${modelName}`);

    // 🎯 鸚鵡模式：直接將用戶輸入的文字轉換為語音
    return textToSpeechWithGemini(text, voiceConfig);
  } catch (error) {
    console.error('TTS 調用失敗:', error);
    return {
      success: false,
      error: error.message,
      modelUsed: config?.ttsModel || 'unknown', // 🔧 修復：使用實際模型名稱而非'error'
      isPlayable: false
    };
  }
}

// 6. 🎙️ 對話音頻專用：TTS 文字轉語音（對話模式）
function callGeminiAudioDialog(text, conversationContext = {}) {
  const config = getConfig();
  
  // 🔧 使用輪換機制獲取 API Key
  let apiKey;
  try {
    apiKey = getCurrentGeminiApiKey();
  } catch (error) {
    console.error('獲取 Gemini API Key 失敗:', error);
    return {
      success: false,
      error: '未設定 Gemini API Key',
      modelUsed: config?.audioDialogModel || 'unknown' // 🔧 修復：使用實際模型名稱
    };
  }

  const modelName = config.audioDialogModel;
  console.log(`🎙️ 使用對話音頻模型: ${modelName}`);

  try {
    // 🎯 對話模式：先用一般模型生成回應，再用 TTS 轉換
    console.log('🎙️ 對話模式：生成智能回應 + TTS 轉換');

    // 構建對話上下文
    let contextPrompt = text;
    if (conversationContext.conversationHistory && conversationContext.conversationHistory.length > 0) {
      const historyText = conversationContext.conversationHistory.map(h => `用戶: ${h.user}\n助理: ${h.assistant}`).join('\n');
      contextPrompt = `對話歷史:\n${historyText}\n\n當前用戶: ${text}`;
    }

    // 1. 使用一般模型生成回應
    let textResponse;
    try {
      textResponse = callGemini(contextPrompt, 'general');
    } catch (geminiError) {
      console.error('對話生成失敗:', geminiError);
      return {
        success: false,
        error: `對話生成失敗: ${geminiError.message}`,
        modelUsed: modelName
      };
    }

    if (!textResponse) {
      console.error('對話生成返回空結果');
      return {
        success: false,
        error: '對話生成返回空結果',
        modelUsed: modelName
      };
    }

    // 2. 使用 TTS 模型轉換為語音
    let ttsResult;
    try {
      ttsResult = textToSpeechWithGemini(textResponse, {
        voiceName: conversationContext.voiceName || 'Kore'
      });
    } catch (ttsError) {
      console.error('TTS 轉換失敗:', ttsError);
      return {
        success: false,
        error: `TTS 轉換失敗: ${ttsError.message}`,
        modelUsed: modelName,
        originalText: textResponse
      };
    }

    if (ttsResult && ttsResult.success) {
      console.log('✅ 對話音頻處理成功');
      return {
        success: true,
        fileName: ttsResult.fileName,
        driveUrl: ttsResult.driveUrl,
        cloudinaryUrl: ttsResult.cloudinaryUrl,
        isPlayable: ttsResult.isPlayable,
        modelUsed: modelName,
        originalText: textResponse,
        userQuery: text,
        mode: 'conversational_tts'
      };
    } else {
      console.error('TTS 轉換失敗:', ttsResult?.error);
      return {
        success: false,
        error: `TTS 轉換失敗: ${ttsResult?.error || '未知錯誤'}`,
        modelUsed: modelName,
        originalText: textResponse
      };
    }

  } catch (error) {
    console.error('對話音頻處理失敗:', error);
    return {
      success: false,
      error: error.message,
      modelUsed: config?.audioDialogModel || 'unknown' // 🔧 修復：使用實際模型名稱而非'error'
    };
  }
}

// 🔧 此函數已被移除，功能已整合到 callGeminiAudioDialog 中
// 保留此註釋以說明變更

// 7. 🎨 圖片生成專用：調用圖片生成模型
function callGeminiImageGeneration(prompt, imageConfig = {}) {
  const config = getConfig();
  if (!config.geminiApiKey) throw new Error('Gemini API Key 未設定');
  
  const modelName = config.imageGenModel;
  console.log(`🎨 使用圖片生成模型: ${modelName}`);
  
  // 使用現有的 generateImageWithGemini 函數，但確保使用正確的模型
  return generateImageWithGemini(prompt, imageConfig);
}

// 8. 📊 語義搜索專用：調用嵌入模型
function callGeminiEmbedding(text, embeddingConfig = {}) {
  const config = getConfig();
  if (!config.geminiApiKey) throw new Error('Gemini API Key 未設定');
  
  const modelName = config.embeddingModel;
  console.log(`📊 使用嵌入模型: ${modelName}`);
  
  // 🚀 這裡需要實現嵌入向量的調用邏輯
  // 暫時返回提示訊息，等待實現
  return {
    success: false,
    message: `📊 嵌入向量功能開發中...\n模型: ${modelName}\n輸入: ${text}`,
    modelUsed: modelName
  };
}

/**
 * 🎛️ 強健的功能開關檢查函數
 * 🔧 修復：處理可能的空白字符和類型問題
 * @param {string} featureName - 功能名稱（不含 '功能開關_' 前綴）
 * @param {string} sheetName - 工作表名稱，預設為 'APIKEY'
 * @returns {boolean} 功能是否啟用
 */
function isFeatureToggleEnabled(featureName, sheetName = 'APIKEY') {
  try {
    const key = `功能開關_${featureName}`;
    const rawValue = getConfigValue(key, sheetName);

    if (!rawValue) {
      console.log(`⚠️ 功能開關 ${key} 未找到或為空，預設為關閉`);
      return false;
    }

    // 多重檢查邏輯，處理各種可能的值格式
    const isEnabled = rawValue === 'TRUE' ||
                     rawValue === true ||
                     rawValue === 'true' ||
                     rawValue.toString().trim().toUpperCase() === 'TRUE';

    console.log(`🎛️ 功能開關檢查: ${key} = "${rawValue}" → ${isEnabled ? '✅ 啟用' : '❌ 關閉'}`);

    return isEnabled;

  } catch (error) {
    console.error(`❌ 檢查功能開關 ${featureName} 失敗:`, error);
    return false;
  }
}

// 🌩️ GCP Access Token 函數已移至 GeminiAdvanced.gs
// 此處保留註釋以說明函數位置

// 9. 統一處理全形半形符號
function normalizeText(text) {
  return text
    .replace(/！/g, '!')  // 全形驚嘆號轉半形
    .replace(/？/g, '?')  // 全形問號轉半形
    .replace(/，/g, ',')  // 全形逗號轉半形（如需要）
    .replace(/。/g, '.')  // 全形句號轉半形（如需要）
    .trim();
}

// 10. 多語言指令對照表
function normalizeCommand(text) {
  const commandMap = {
    // 筆記功能
    '记录': '記錄',     // 簡體
    'note': '記錄',     // 英文
    'record': '記錄',   // 英文
    'memo': '記錄',     // 英文
    
    // 測試功能  
    '测试': '測試',     // 簡體
    'test': '測試',     // 英文
    
    // 幫助功能
    '帮助': '幫助',     // 簡體
    'help': '幫助',     // 英文
    'assistance': '幫助', // 英文
    
    // Threads功能
    't': 't',           // 保持原樣
    'threads': 't',     // 英文全名
    '发文': 't',        // 簡體
    '發文': 't',        // 繁體
    
    // 檔案記憶相關指令
    '分析': '分析',     // 繁體
    'analyze': '分析',  // 英文
    'analyse': '分析',  // 英文
    '查看': '查看',     // 繁體
    'view': '查看',     // 英文
    'show': '查看'      // 英文
  };
  
  // 分離指令和內容
  const parts = text.split(' ');
  const command = parts[0].toLowerCase();
  const content = parts.slice(1).join(' ');
  
  // 轉換指令
  const normalizedCommand = commandMap[command] || command;
  
  return content ? `${normalizedCommand} ${content}` : normalizedCommand;
}

// 11. 回覆訊息到 LINE
function replyMessage(replyToken, text) {
  try {
    const startTime = Date.now(); // 🕐 開始計時
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    const url = 'https://api.line.me/v2/bot/message/reply';
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: [{ type: 'text', text: text }]
    });

    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      payload: payload
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`LINE API 回應錯誤: ${response.getContentText()}`);
    }

    // 🕐 記錄成功的回覆（包含回應時間）
    const responseTime = Date.now() - startTime;
    logActivity('Reply', '文字回覆成功', 'Success', 'text', 'replyMessage', `訊息長度: ${text.length}字`, responseTime);

  } catch (error) {
    console.error('回覆訊息錯誤:', error);
    const errorResponseTime = typeof startTime !== 'undefined' ? Date.now() - startTime : null;
    logActivity('Reply', '回覆錯誤', 'Failure', 'text', 'replyMessage', error.toString(), errorResponseTime);
  }
}

/**
 * 🎵 AI-First 音頻檔案回覆（修復播放按鈕版本）
 * 智能決定是發送實際音頻檔案還是包含連結的文字訊息
 * 🔧 v2.1 - 修復播放按鈕被淹沒問題：成功時只發送音頻，失敗時才發送文字說明
 */
function replyWithAudio(replyToken, audioResult, originalText) {
  try {
    const startTime = Date.now(); // 🕐 開始計時
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    console.log(`🔊 嘗試發送音頻回覆，可播放: ${audioResult.isPlayable}`);

    // 🤖 AI 決定回覆策略：優先使用 Cloudinary M4A 格式
    const messages = [];
    let audioSent = false; // 🔧 新增：追蹤音頻是否成功發送

    // 🎯 嘗試發送可播放的 M4A 音頻檔案
    if (audioResult.isPlayable && audioResult.cloudinaryUrl) {
      try {
        console.log(`✅ 使用 Cloudinary M4A 音頻: ${audioResult.cloudinaryUrl}`);
        
        // 估算音頻時長（基於文字長度，實際應該從 TTS API 獲取）
        const estimatedDuration = Math.min(Math.max(originalText.length * 150, 1000), 300000); // 1秒到5分鐘
        
        // 添加可播放的 M4A 音頻訊息
        messages.push({
          type: 'audio',
          originalContentUrl: audioResult.cloudinaryUrl,
          duration: estimatedDuration
        });

        audioSent = true; // 🔧 標記音頻發送成功
        console.log(`🎵 音頻訊息已準備，時長: ${estimatedDuration}ms`);

      } catch (audioError) {
        console.log('Cloudinary 音頻發送失敗，改為文字回覆:', audioError);
        audioSent = false; // 🔧 標記音頻發送失敗
      }
    } else {
      console.log('⚠️ 沒有可播放的音頻 URL，將發送文字回覆');
      audioSent = false; // 🔧 標記音頻發送失敗
    }

    // 🎯 修復：只有在音頻發送失敗時才添加文字說明
    if (!audioSent) {
      const smartResponse = generateAudioFallbackText(originalText, audioResult);
      messages.push({
        type: 'text',
        text: smartResponse
      });
      console.log('📝 音頻發送失敗，添加文字說明');
    } else {
      console.log('🎵 音頻發送成功，保持播放按鈕乾淨顯示（不添加文字訊息）');
    }

    const url = 'https://api.line.me/v2/bot/message/reply';
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: messages
    });

    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      payload: payload
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`LINE API 回應錯誤: ${response.getContentText()}`);
    }

    console.log(`✅ 音頻回覆成功發送，包含 ${messages.length} 個訊息`);
    const responseTime = Date.now() - startTime; // 🕐 計算回應時間
    logActivity('Reply', '音頻回覆成功', 'Success', 'audio', 'replyWithAudio', `可播放: ${audioResult.isPlayable}, 訊息數: ${messages.length}, 純音頻: ${audioSent}`, responseTime);

  } catch (error) {
    console.error('音頻回覆錯誤:', error);
    // 備用：發送純文字回覆
    const fallbackText = generateAudioFallbackText(originalText, audioResult);
    replyMessage(replyToken, fallbackText);
  }
}

/**
 * 🌟 Flex Message 圖片回覆（v4.1 - 互動按鈕版）
 * 🎯 v1.6.4 需求：2個功能按鈕，故事接龍改用指令
 * 🔘 2個按鈕：📊生成詳情、📤分享卡片
 * 🎪 故事接龍：使用指令 `！故事接龍 新內容`
 *
 * 📋 調用鏈支援：
 * 🔵 被動發圖鏈：handleAIImageGeneration → replyWithImage (imageStory有值)
 * 🟡 偽主動發圖鏈：execute_reply_cost_saver_response → replyWithImage (imageStory需傳入)
 */
function replyWithImage(replyToken, imageResult, originalPrompt, userId = null, imageStory = null) {
  // 🎲 準備顯示資訊（移到外面避免 catch 塊中 undefined）
  const seedValue = imageResult.seed || Math.floor(Math.random() * 1000000); // 備用隨機SEED
  const modelName = imageResult.modelUsed || '未知模型';

  try {
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    // 🔧 v1.6.5 修正：將imageStory添加到imageResult中，供分享按鈕使用
    console.log(`🔍 [調試] replyWithImage收到的imageStory參數: ${imageStory ? imageStory.substring(0, 50) + '...' : 'null'}`);
    if (imageStory) {
      imageResult.imageStory = imageStory;
      console.log(`📖 已將故事內容添加到imageResult中，長度: ${imageStory.length}`);
    } else {
      console.log(`📖 無故事內容傳入，imageResult.imageStory將為undefined`);
    }
    console.log(`🔍 [調試] 分享按鈕將使用: ${imageResult.imageStory || '生成失敗'}`);

    console.log(`🎨 使用 Flex Message v4.0 發送圖片+互動按鈕回覆`);

    // 📋 準備圖片 URL（優先使用 Cloudinary）
    let imageUrl;
    if (imageResult.cloudinaryUrl) {
      imageUrl = imageResult.cloudinaryUrl;
      console.log(`✅ 使用 Cloudinary 圖片: ${imageUrl}`);
    } else if (imageResult.driveUrl) {
      // Google Drive 需要轉換為可公開顯示的格式
      imageUrl = imageResult.driveUrl.replace('/view?usp=sharing', '').replace('/file/d/', '/uc?export=view&id=').replace('/edit?usp=sharing', '');
      console.log(`📁 使用 Google Drive 圖片: ${imageUrl}`);
    } else {
      throw new Error('無法獲取有效的圖片 URL');
    }
    
    console.log(`🎲 SEED: ${seedValue}, 🤖 模型: ${modelName}`);

    // 🌟 構建 v4.0 Flex Message（含4個互動按鈕）
    // 🔧 限制 altText 長度不超過 350 字符
    const maxPromptLength = 300;
    const truncatedPrompt = originalPrompt.length > maxPromptLength
      ? originalPrompt.substring(0, maxPromptLength) + '...'
      : originalPrompt;

    const flexMessage = {
      type: "flex",
      altText: `🎨 ${truncatedPrompt} (SEED: ${seedValue})`,
      contents: {
        type: "bubble",
        hero: {
          type: "image",
          url: imageUrl,
          size: "full",
          aspectRatio: "1:1",
          aspectMode: "cover",
          action: {
            type: "uri",
            uri: imageUrl // 點擊圖片查看原圖
          }
        },
        body: {
          type: "box",
          layout: "vertical",
          spacing: "md",
          paddingAll: "20px",
          contents: [
            // 🆕 v1.6.3：顯示用戶輸入的提示詞，而不是"AI圖片生成完成"
            {
              type: "text",
              text: originalPrompt,
              size: "lg",
              weight: "bold",
              color: "#333333",
              wrap: true
            }
          ]
        },
        footer: {
          type: "box",
          layout: "vertical",
          spacing: "sm",
          paddingAll: "20px",
          contents: [
//            // 🔘 第一排按鈕：生成詳情
//            {
//              type: "box",
//              layout: "horizontal",
//              spacing: "sm",
//              contents: [
//                {
//                  type: "button",
//                  action: {
//                    type: "postback",
//                    label: "📊 生成詳情",
//                    data: createSafePostbackDataV2('show_details', seedValue, modelName, originalPrompt)
//                  },
//                  style: "primary",
//                  color: "#4A90E2",
//                  height: "sm"
//                }
//              ]
//            },
            // 🔘 第二排按鈕：分享卡片（移除故事接龍按鈕，改用指令）
            {
              type: "box",
              layout: "horizontal",
              spacing: "sm",
              contents: [
                {
                  type: "button",
                  action: {
                    type: "postback",
                    label: "📤 分享故事",
                    data: createSafePostbackDataV2('share_story', seedValue, modelName, originalPrompt, {
                      imageUrl: imageUrl,
                      imageStory: imageStory,  // 🔧 修復：使用傳入的imageStory參數而不是imageResult.imageStory
                      seed: seedValue  // 🔧 添加seed以便備用查找
                    })
                  },
                  style: "primary",
                  color: "#50C878",
                  height: "sm"
                }
              ]
            },
            // 🆕 v4.1：底部文字 + SEED 顯示
            {
              type: "box",
              layout: "horizontal",
              margin: "md",
              contents: [
                {
                  type: "text",
                  text: config.footer,
                  size: "xs",
                  color: "#999999",
                  flex: 1
                },
                {
                  type: "text",
                  text: `${seedValue}`,
                  size: "xs",
                  color: "#999999",
                  align: "end"
                }
              ]
            }
          ]
        }
      }
    };

    // 📤 發送 Flex Message
    const url = 'https://api.line.me/v2/bot/message/reply';
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: [flexMessage]
    });

    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      payload: payload
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`LINE API 回應錯誤: ${response.getContentText()}`);
    }

    console.log(`✅ Flex Message v4.0 圖片回覆成功發送，SEED: ${seedValue}`);

    // 🆕 v1.6.3：記錄圖片歷史（用於故事接龍功能）
    if (userId) {
      recordImageHistory(userId, seedValue, originalPrompt, imageUrl, modelName);
    } else {
      console.log(`⚠️ 無法記錄圖片歷史：userId 未提供`);
    }

    logActivity('Reply', 'Flex圖片回覆v4.1成功', 'Success', 'image', 'sendFlexImageReplyV4', `SEED: ${seedValue}, 模型: ${modelName}, 按鈕數: 3, Cloudinary: ${!!imageResult.cloudinaryUrl}`);

  } catch (error) {
    console.error('Flex Message v4.0 圖片回覆錯誤:', error);
    
    // 🆘 備用方案：使用傳統的分離訊息，並顯示錯誤原因
    console.log('🔄 改用備用方案：分離發送圖片和文字，並報告錯誤');
    
    try {
      // 🔧 重新獲取 config（修復 "config is not defined" 錯誤）
      const fallbackConfig = getConfig();

      // 先發送圖片
      let originalContentUrl, previewImageUrl;
      
      if (imageResult.cloudinaryUrl) {
        originalContentUrl = imageResult.cloudinaryUrl;
        previewImageUrl = imageResult.cloudinaryUrl.replace('/upload/', '/upload/w_240,h_240,c_fit/');
      } else if (imageResult.fileId) {
        originalContentUrl = `https://drive.google.com/uc?export=view&id=${imageResult.fileId}`;
        previewImageUrl = `https://drive.google.com/thumbnail?id=${imageResult.fileId}&sz=w240`;
      } else {
        throw new Error('備用方案也無法獲取圖片 URL');
      }

      // 🚨 錯誤報告訊息（要求用戶通知開發者）
      const errorReportText = `🎨 圖片生成失敗

🎲 SEED: ${seedValue || 'N/A'}
🤖 模型: ${imageResult.modelUsed || '未知模型'}

⚠️ 互動按鈕載入失敗
🔧 錯誤原因: ${error.message}

📞 請協助回報開發者：
- 截圖此訊息
- 註明發生時間: ${new Date().toLocaleString('zh-TW')}
- 聯繫管理員進行修復

💡 您仍可正常使用圖片，但暫時無法使用生成詳情、分享故事等互動功能。`;

      const fallbackMessages = [
        {
          type: 'image',
          originalContentUrl: originalContentUrl,
          previewImageUrl: previewImageUrl
        },
        {
          type: 'text',
          text: errorReportText
        }
      ];

      const fallbackUrl = 'https://api.line.me/v2/bot/message/reply';
      const fallbackPayload = JSON.stringify({
        replyToken: replyToken,
        messages: fallbackMessages
      });

      const fallbackResponse = UrlFetchApp.fetch(fallbackUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + fallbackConfig.lineChannelAccessToken
        },
        payload: fallbackPayload
      });

      if (fallbackResponse.getResponseCode() === 200) {
        console.log('✅ 備用方案發送成功，已提示用戶回報錯誤');
        logActivity('Reply', '備用圖片回覆成功', 'Success', 'image', 'replyWithImage', `SEED: ${imageResult.seed}, 錯誤原因: ${error.message}, 已要求用戶回報`);
      } else {
        throw new Error(`備用方案也失敗: ${fallbackResponse.getContentText()}`);
      }

    } catch (fallbackError) {
      console.error('備用方案也失敗:', fallbackError);
      // 最後的備用：純文字訊息，包含完整錯誤報告
      const finalFallbackText = `🎨 圖片生成失敗

🎲 SEED: ${imageResult.seed || 'N/A'}
🤖 模型: ${imageResult.modelUsed || '未知模型'}

❌ 系統錯誤 - 圖片顯示失敗
🔧 主要錯誤: ${error.message}
🔧 備用錯誤: ${fallbackError.message}

📞 請緊急聯繫開發者：
- 錯誤時間: ${new Date().toLocaleString('zh-TW')}
- 錯誤程度: 嚴重（圖片無法顯示）
- 建議立即檢查系統配置

📁 請查看 Google Drive 中的圖片檔案作為備用

🖼️ 圖片連結：
${imageResult.cloudinaryUrl || imageResult.driveUrl || '連結獲取失敗'}

💡 您可以點擊上方連結查看圖片`;

      replyMessage(replyToken, finalFallbackText);

      // 記錄嚴重錯誤
      logActivity('System', '嚴重圖片回覆錯誤', 'Failure', 'image', 'replyWithImage', `主要錯誤: ${error.message}, 備用錯誤: ${fallbackError.message}, SEED: ${imageResult.seed}`);
    }
  }
}

// 🆕 createDetailsFlexMessage 函數已移至 AIHandlers_Specialized.gs
// 此處保留註釋以說明函數位置

/**
 * 🆘 音頻備用回覆文字
 */
function generateAudioFallbackText(originalText, audioResult) {
  let fallbackText = `🔊 語音轉換完成！\n\n📄 內容：${originalText}\n🎵 檔案：${audioResult.fileName}`;

  if (audioResult.isPlayable && audioResult.cloudinaryUrl) {
    fallbackText += `\n✅ 可播放音頻已準備就緒`;
  } else {
    fallbackText += `\n⚠️ 需要設定 Cloudinary 以啟用音頻播放`;
    if (audioResult.driveUrl) {
      fallbackText += `\n💾 下載連結：${audioResult.driveUrl}`;
    }
  }

  return fallbackText;
}

/**
 * 🆘 圖片備用回覆文字
 */
function generateImageFallbackText(originalPrompt, imageResult) {
  let fallbackText = `🎨 備用圖片生成完成！\n\n📝 描述：${originalPrompt}\n🖼️ 檔案：${imageResult.fileName}`;

  if (imageResult.cloudinaryUrl) {
    fallbackText += `\n✅ 高品質圖片已上傳到 Cloudinary`;
  } else {
    fallbackText += `\n💾 Google Drive 連結：${imageResult.driveUrl}`;
  }

  return fallbackText;
}

/**
 * 🆕 記錄圖片生成歷史（用於故事接龍功能）
 */
function recordImageHistory(userId, seed, originalPrompt, imageUrl, modelName) {
  try {
    const sheet = getOrCreateSheet('ImageHistory');

    // 初始化表頭
    if (sheet.getLastRow() === 0) {
      sheet.appendRow(['時間戳', '用戶ID', 'SEED', '原始提示詞', '圖片URL', '模型名稱']);
    }

    const timestamp = new Date();
    sheet.appendRow([timestamp, userId, seed, originalPrompt, imageUrl, modelName]);

    console.log(`📸 記錄圖片歷史: 用戶=${userId}, SEED=${seed}, 提示詞=${originalPrompt.substring(0, 30)}...`);

    // 保持歷史記錄在合理範圍內（每用戶最多保留50條）
    cleanupImageHistory(sheet, userId);

  } catch (error) {
    console.error('記錄圖片歷史錯誤:', error);
  }
}

/**
 * 🔍 查找用戶最近的圖片記錄（用於故事接龍）
 */
function findRecentImageHistory(userId) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('ImageHistory');
    if (!sheet || sheet.getLastRow() <= 1) {
      console.log(`📸 沒有找到用戶 ${userId} 的圖片歷史記錄`);
      return null;
    }

    const data = sheet.getDataRange().getValues();

    // 從最新記錄開始查找該用戶的圖片
    for (let i = data.length - 1; i >= 1; i--) {
      if (data[i][1] === userId) { // 用戶ID匹配
        const record = {
          timestamp: data[i][0],
          userId: data[i][1],
          seed: data[i][2],
          originalPrompt: data[i][3],
          imageUrl: data[i][4],
          modelName: data[i][5]
        };

        console.log(`📸 找到用戶 ${userId} 最近的圖片記錄: SEED=${record.seed}, 提示詞=${record.originalPrompt.substring(0, 30)}...`);
        return record;
      }
    }

    console.log(`📸 沒有找到用戶 ${userId} 的圖片記錄`);
    return null;

  } catch (error) {
    console.error('查找圖片歷史錯誤:', error);
    return null;
  }
}

/**
 * 🧹 清理圖片歷史記錄（保持每用戶最多50條）
 */
function cleanupImageHistory(sheet, userId) {
  try {
    const data = sheet.getDataRange().getValues();
    const userRecords = [];

    // 收集該用戶的所有記錄
    for (let i = 1; i < data.length; i++) {
      if (data[i][1] === userId) {
        userRecords.push({ row: i + 1, timestamp: data[i][0] });
      }
    }

    // 如果超過50條，刪除最舊的記錄
    if (userRecords.length > 50) {
      userRecords.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      const toDelete = userRecords.slice(0, userRecords.length - 50);

      // 從後往前刪除（避免行號變化）
      toDelete.reverse().forEach(record => {
        sheet.deleteRow(record.row);
      });

      console.log(`🧹 清理用戶 ${userId} 的舊圖片記錄，刪除 ${toDelete.length} 條`);
    }

  } catch (error) {
    console.error('清理圖片歷史錯誤:', error);
  }
}

// 12. 記錄活動日誌 - v2.3 添加彩色EMOJI標記每段LOG開頭
function logActivity(communicationType, action, apiStatus, messageType, sourceFunction, details, responseTime = null) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    if (!sheet) {
      console.error('找不到活動日誌工作表');
      return;
    }

    // 🕐 創建完整的時間戳（包含日期和時分秒）
    const now = new Date();
    const formattedTimestamp = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy/M/d HH:mm:ss');

    // 🎨 檢查是否為新的LOG段落開頭（Webhook收到訊息）
    const isNewLogSegment = sourceFunction === 'handleLineEvent' && action === '收到訊息';

    // 🔵 統一使用藍色圓形符號
    const unifiedEmoji = '🔵';

    // 🆕 Push 通訊方式加上警告標示
    const displayCommunicationType = communicationType === 'Push' ? '⚠️Push' : communicationType;

    // 🔧 處理回應時間：只有有效的數字才顯示ms格式
    let responseTimeDisplay = '';
    if (responseTime !== null && responseTime !== undefined) {
      if (typeof responseTime === 'number' && responseTime > 0) {
        responseTimeDisplay = `${responseTime}ms`;
      } else if (typeof responseTime === 'string' && responseTime.includes('ms')) {
        responseTimeDisplay = responseTime;
      } else {
        console.warn(`⚠️ 無效的回應時間值: "${responseTime}", 類型: ${typeof responseTime}`);
        console.warn(`📝 函數調用: ${sourceFunction}, 動作: ${action}`);
        responseTimeDisplay = ''; // 清空無效值
      }
    }

    // 🎯 為新LOG段落的詳細資訊添加統一EMOJI
    let enhancedDetails = details;
    if (isNewLogSegment) {
      enhancedDetails = `${unifiedEmoji} ${details}`;
    }

    // 🎯 按照用戶的確切欄位順序寫入數據
    const newRow = [
      formattedTimestamp,           // A欄: 時間戳 (完整日期+時間)
      displayCommunicationType,     // B欄: 通訊方式
      responseTimeDisplay,          // C欄: 回應時間 (XXXms 格式)
      action,                      // D欄: 動作
      apiStatus,                   // E欄: API狀態
      messageType,                 // F欄: 訊息類型
      sourceFunction,              // G欄: 來源函數
      enhancedDetails              // H欄: 詳細資訊（新LOG段落有彩色EMOJI）
    ];

    // 🚀 插入到第2行（最新記錄置頂）
    if (sheet.getLastRow() > 1) {
      sheet.insertRowAfter(1);
      sheet.getRange(2, 1, 1, newRow.length).setValues([newRow]);
    } else {
      sheet.appendRow(newRow);
    }

    // 🧹 保持日誌在合理範圍內
    if (sheet.getLastRow() > 1000) {
      const deleteCount = 100;
      const startRow = sheet.getLastRow() - deleteCount + 1;
      sheet.deleteRows(startRow, deleteCount);
      console.log(`🧹 清理舊日誌記錄，刪除最後 ${deleteCount} 行`);
    }

    // 🎯 記錄成功信息
    if (responseTimeDisplay) {
      console.log(`⏱️ 回應時間: ${responseTimeDisplay} - ${action}`);
    }

    console.log(`✅ 日誌記錄成功: ${action} (時間: ${formattedTimestamp})`);

  } catch (error) {
    console.error('記錄活動日誌錯誤:', error);
  }
}

// 🔄 向後兼容：舊版 logActivity 函數（僅供緊急使用）
function logActivityLegacy(action, details) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    if (sheet) {
      const timestamp = new Date();
      // 使用舊格式：只填入時間戳、動作（放在通訊方式欄位）、詳細資訊（放在詳細資訊欄位）
      sheet.appendRow([timestamp, '', action, '', '', '', details]);
    }
  } catch (error) {
    console.error('記錄活動日誌錯誤:', error);
  }
}

// 13. 保存文字筆記
function saveTextNote(userId, content, sourceType) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('筆記暫存');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, userId, sourceType, content]);
      logActivity('System', '儲存筆記', 'Success', 'text', 'saveTextNote', `用戶${userId}: ${content.substring(0, 30)}...`);
    }
  } catch (error) {
    console.error('儲存筆記錯誤:', error);
  }
}

// 14. 取得媒體類型顯示名稱
function getMediaTypeDisplay(type) {
  const typeMap = {
    'image': '圖片',
    'video': '影片', 
    'audio': '音訊',
    'file': '檔案'
  };
  return typeMap[type] || '媒體檔案';
}

// 15. 記錄檔案日誌
function logFileActivity(userId, fileName, fileUrl, fileSize, mediaType = 'file', sourceType = 'user') {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('檔案日誌');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, userId, fileName, fileUrl, fileSize, mediaType, sourceType]);
    }
  } catch (error) {
    console.error('記錄檔案日誌錯誤:', error);
  }
}

// 16. 取得檔案類型資訊
function getFileTypeInfo(fileName, mimeType) {
  const fileExtension = fileName.split('.').pop().toLowerCase();
  
  // 檔案類型對應表
  const typeMap = {
    // 文檔類型
    'pdf': { icon: '📄', description: 'PDF 文件' },
    'docx': { icon: '📝', description: 'Word 文件' },
    'doc': { icon: '📝', description: 'Word 文件（舊版）' },
    'xlsx': { icon: '📊', description: 'Excel 試算表' },
    'xls': { icon: '📊', description: 'Excel 試算表（舊版）' },
    'pptx': { icon: '🎯', description: 'PowerPoint 簡報' },
    'ppt': { icon: '🎯', description: 'PowerPoint 簡報（舊版）' },
    
    // Google 文件
    'google-apps.document': { icon: '📝', description: 'Google 文件' },
    'google-apps.spreadsheet': { icon: '📊', description: 'Google 試算表' },
    'google-apps.presentation': { icon: '🎯', description: 'Google 簡報' },
    
    // 文字和代碼
    'txt': { icon: '📝', description: '純文字檔案' },
    'md': { icon: '📋', description: 'Markdown 文件' },
    'csv': { icon: '📊', description: 'CSV 資料檔' },
    'json': { icon: '🔧', description: 'JSON 資料檔' },
    'xml': { icon: '🔧', description: 'XML 檔案' },
    'log': { icon: '📝', description: '日誌檔案' },
    'rtf': { icon: '📝', description: 'RTF 格式文件' },
    
    // 圖片
    'jpg': { icon: '🖼️', description: 'JPEG 圖片' },
    'jpeg': { icon: '🖼️', description: 'JPEG 圖片' },
    'png': { icon: '🖼️', description: 'PNG 圖片' },
    'gif': { icon: '🖼️', description: 'GIF 圖片' },
    'bmp': { icon: '🖼️', description: 'BMP 圖片' },
    
    // 影音
    'mp4': { icon: '🎬', description: 'MP4 影片' },
    'avi': { icon: '🎬', description: 'AVI 影片' },
    'mp3': { icon: '🎵', description: 'MP3 音訊' },
    'm4a': { icon: '🎵', description: 'M4A 音訊' },
    
    // 壓縮檔
    'zip': { icon: '📦', description: 'ZIP 壓縮檔' },
    'rar': { icon: '📦', description: 'RAR 壓縮檔' },
    '7z': { icon: '📦', description: '7Z 壓縮檔' }
  };
  
  // 先檢查 MIME 類型
  if (mimeType.includes('google-apps')) {
    const appType = mimeType.split('.').pop();
    return typeMap[`google-apps.${appType}`] || { icon: '📁', description: 'Google Apps 檔案' };
  }
  
  // 再檢查副檔名
  return typeMap[fileExtension] || { icon: '📁', description: '未知檔案類型' };
}

// 17. 獲取版本資訊（統一從 Code.gs 獲取）
function getVersionInfo() {
  return {
    version: SCRIPT_VERSION,  // 這個變數現在定義在 Code.gs 頂部
  };
}
