/*
 * 檔案: 20250717_143000_function_duplicate_scanner_debug.gs
 * 分類: test
 * 功能開關: -
 * 描述: 自動化掃描所有 .gs 檔案中的函數定義，檢查重複函數名稱
 * 依賴: [core_utils.gs]
 * 最後更新: 2025-07-17
 */

/**
 * 主要的函數重複掃描器
 * 掃描所有 .gs 檔案並檢查函數名稱重複
 */
function 函數重複掃描器_debug() {
  console.log('🔍 開始掃描函數重複問題...');
  
  try {
    const scanResult = scanAllFunctionDefinitions();
    const duplicateReport = analyzeFunctionDuplicates(scanResult);
    const forbiddenReport = checkForbiddenFunctionNames(scanResult);
    
    displayScanResults(duplicateReport, forbiddenReport, scanResult);
    
    return {
      success: true,
      totalFunctions: scanResult.totalFunctions,
      duplicates: duplicateReport.duplicates.length,
      forbidden: forbiddenReport.forbidden.length
    };
    
  } catch (error) {
    console.error('❌ 函數掃描過程中發生錯誤:', error);
    return { success: false, error: error.toString() };
  }
}

/**
 * 掃描所有 .gs 檔案中的函數定義
 */
function scanAllFunctionDefinitions() {
  const files = DriveApp.getFolderById(getScriptId()).getFiles();
  const functionMap = new Map(); // 函數名 -> [檔案列表]
  const fileList = [];
  let totalFunctions = 0;
  
  while (files.hasNext()) {
    const file = files.next();
    const fileName = file.getName();
    
    // 只處理 .gs 檔案
    if (!fileName.endsWith('.gs')) continue;
    
    try {
      const content = file.getBlob().getDataAsString();
      const functions = extractFunctionNames(content, fileName);
      
      fileList.push({
        name: fileName,
        functions: functions
      });
      
      // 建立函數名稱對應表
      functions.forEach(funcName => {
        if (!functionMap.has(funcName)) {
          functionMap.set(funcName, []);
        }
        functionMap.get(funcName).push(fileName);
        totalFunctions++;
      });
      
    } catch (error) {
      console.warn(`⚠️ 無法讀取檔案 ${fileName}:`, error);
    }
  }
  
  return {
    functionMap: functionMap,
    fileList: fileList,
    totalFunctions: totalFunctions
  };
}

/**
 * 從檔案內容中提取函數名稱
 */
function extractFunctionNames(content, fileName) {
  const functions = [];
  
  // 匹配函數定義的正則表達式
  const functionRegex = /^[\s]*function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/gm;
  let match;
  
  while ((match = functionRegex.exec(content)) !== null) {
    const functionName = match[1];
    functions.push(functionName);
  }
  
  return functions;
}

/**
 * 分析函數重複情況
 */
function analyzeFunctionDuplicates(scanResult) {
  const duplicates = [];
  
  scanResult.functionMap.forEach((files, functionName) => {
    if (files.length > 1) {
      duplicates.push({
        functionName: functionName,
        files: files,
        count: files.length
      });
    }
  });
  
  // 按重複次數排序
  duplicates.sort((a, b) => b.count - a.count);
  
  return { duplicates: duplicates };
}

/**
 * 檢查禁止的通用函數名稱
 */
function checkForbiddenFunctionNames(scanResult) {
  const forbiddenNames = [
    'doPost', 'doGet', 'main', 'init', 'run', 'process', 'handle',
    'test', 'debug', 'check', 'execute', 'start', 'stop'
  ];
  
  const forbidden = [];
  
  scanResult.functionMap.forEach((files, functionName) => {
    // 檢查是否為禁止的通用名稱
    if (forbiddenNames.includes(functionName)) {
      // doPost 和 doGet 允許在主要檔案中存在
      if ((functionName === 'doPost' && files.includes('modules_line_webhook.gs')) ||
          (functionName === 'doGet' && files.length === 1)) {
        return; // 允許的例外情況
      }
      
      forbidden.push({
        functionName: functionName,
        files: files,
        reason: '使用了禁止的通用函數名稱'
      });
    }
    
    // 檢查測試函數命名規範
    if (functionName.includes('test') || functionName.includes('debug') || functionName.includes('disposable')) {
      files.forEach(fileName => {
        if (!fileName.startsWith('_test/') && !fileName.includes('_test')) {
          forbidden.push({
            functionName: functionName,
            files: [fileName],
            reason: '測試函數應該放在 _test 目錄或使用適當後綴'
          });
        }
      });
    }
  });
  
  return { forbidden: forbidden };
}

/**
 * 顯示掃描結果
 */
function displayScanResults(duplicateReport, forbiddenReport, scanResult) {
  console.log('\n📊 函數重複掃描結果報告');
  console.log('=' .repeat(50));
  
  // 基本統計
  console.log(`📁 掃描檔案數量: ${scanResult.fileList.length}`);
  console.log(`🔧 總函數數量: ${scanResult.totalFunctions}`);
  console.log(`⚠️ 重複函數數量: ${duplicateReport.duplicates.length}`);
  console.log(`🚫 禁止函數數量: ${forbiddenReport.forbidden.length}`);
  
  // 重複函數詳情
  if (duplicateReport.duplicates.length > 0) {
    console.log('\n🔴 發現重複函數:');
    duplicateReport.duplicates.forEach(duplicate => {
      console.log(`  ❌ ${duplicate.functionName} (出現 ${duplicate.count} 次)`);
      duplicate.files.forEach(file => {
        console.log(`     📄 ${file}`);
      });
    });
  } else {
    console.log('\n✅ 沒有發現重複函數');
  }
  
  // 禁止函數詳情
  if (forbiddenReport.forbidden.length > 0) {
    console.log('\n🚫 發現禁止的函數名稱:');
    forbiddenReport.forbidden.forEach(forbidden => {
      console.log(`  ❌ ${forbidden.functionName}: ${forbidden.reason}`);
      forbidden.files.forEach(file => {
        console.log(`     📄 ${file}`);
      });
    });
  } else {
    console.log('\n✅ 沒有發現禁止的函數名稱');
  }
  
  // 檔案函數統計
  console.log('\n📋 各檔案函數統計:');
  scanResult.fileList
    .sort((a, b) => b.functions.length - a.functions.length)
    .forEach(file => {
      console.log(`  📄 ${file.name}: ${file.functions.length} 個函數`);
    });
}

/**
 * 獲取當前腳本的 ID
 */
function getScriptId() {
  return ScriptApp.getScriptId();
}

/**
 * 快速檢查 - 只顯示重複函數
 */
function 快速重複檢查_debug() {
  console.log('🚀 快速重複函數檢查...');
  
  const scanResult = scanAllFunctionDefinitions();
  const duplicateReport = analyzeFunctionDuplicates(scanResult);
  
  if (duplicateReport.duplicates.length === 0) {
    console.log('✅ 沒有發現重複函數');
    return { success: true, duplicates: 0 };
  }
  
  console.log(`⚠️ 發現 ${duplicateReport.duplicates.length} 個重複函數:`);
  duplicateReport.duplicates.forEach(duplicate => {
    console.log(`❌ ${duplicate.functionName}: ${duplicate.files.join(', ')}`);
  });
  
  return { 
    success: true, 
    duplicates: duplicateReport.duplicates.length,
    details: duplicateReport.duplicates 
  };
}

/**
 * 生成函數清單報告並更新文檔
 */
function 生成函數清單報告_debug() {
  console.log('📝 生成函數清單報告...');
  
  const scanResult = scanAllFunctionDefinitions();
  const duplicateReport = analyzeFunctionDuplicates(scanResult);
  const forbiddenReport = checkForbiddenFunctionNames(scanResult);
  
  const report = generateDetailedFunctionReport(scanResult, duplicateReport, forbiddenReport);
  
  console.log('📄 生成的報告內容:');
  console.log(report);
  
  // 提供更新函數清單.md的建議
  console.log('\n💡 建議操作:');
  console.log('1. 複製上述報告內容');
  console.log('2. 更新 函數清單.md 中的實際函數清單部分');
  console.log('3. 如有重複函數，請參考 steering 規範進行修復');
  
  return {
    report: report,
    duplicates: duplicateReport.duplicates.length,
    forbidden: forbiddenReport.forbidden.length,
    totalFunctions: scanResult.totalFunctions
  };
}

/**
 * 生成詳細的函數報告
 */
function generateDetailedFunctionReport(scanResult, duplicateReport, forbiddenReport) {
  const report = [];
  
  report.push('# 自動生成函數清單報告');
  report.push(`生成時間: ${new Date().toLocaleString('zh-TW')}`);
  report.push(`總檔案數: ${scanResult.fileList.length}`);
  report.push(`總函數數: ${scanResult.totalFunctions}`);
  report.push(`重複函數: ${duplicateReport.duplicates.length}`);
  report.push(`禁止函數: ${forbiddenReport.forbidden.length}`);
  report.push('');
  
  // 重複函數警告
  if (duplicateReport.duplicates.length > 0) {
    report.push('## ⚠️ 發現重複函數');
    duplicateReport.duplicates.forEach(duplicate => {
      report.push(`- **${duplicate.functionName}** (${duplicate.count}次): ${duplicate.files.join(', ')}`);
    });
    report.push('');
  }
  
  // 禁止函數警告
  if (forbiddenReport.forbidden.length > 0) {
    report.push('## 🚫 發現禁止函數');
    forbiddenReport.forbidden.forEach(forbidden => {
      report.push(`- **${forbidden.functionName}**: ${forbidden.reason}`);
      report.push(`  - 檔案: ${forbidden.files.join(', ')}`);
    });
    report.push('');
  }
  
  // 按檔案分組的函數清單
  report.push('## 📋 按檔案分組的函數清單');
  
  // 按類型分組
  const coreFiles = scanResult.fileList.filter(f => f.name.startsWith('core_'));
  const moduleFiles = scanResult.fileList.filter(f => f.name.startsWith('modules_'));
  const configFiles = scanResult.fileList.filter(f => f.name.startsWith('config_'));
  const testFiles = scanResult.fileList.filter(f => f.name.includes('test') || f.name.includes('_test'));
  const otherFiles = scanResult.fileList.filter(f => 
    !f.name.startsWith('core_') && 
    !f.name.startsWith('modules_') && 
    !f.name.startsWith('config_') && 
    !f.name.includes('test')
  );
  
  // 核心檔案
  if (coreFiles.length > 0) {
    report.push('### 🔧 核心系統檔案');
    coreFiles.sort((a, b) => a.name.localeCompare(b.name)).forEach(file => {
      report.push(`#### ${file.name}`);
      if (file.functions.length === 0) {
        report.push('- (無函數)');
      } else {
        file.functions.sort().forEach(func => {
          report.push(`- ${func}()`);
        });
      }
      report.push('');
    });
  }
  
  // 模組檔案
  if (moduleFiles.length > 0) {
    report.push('### 📦 功能模組檔案');
    moduleFiles.sort((a, b) => a.name.localeCompare(b.name)).forEach(file => {
      report.push(`#### ${file.name}`);
      if (file.functions.length === 0) {
        report.push('- (無函數)');
      } else {
        file.functions.sort().forEach(func => {
          report.push(`- ${func}()`);
        });
      }
      report.push('');
    });
  }
  
  // 配置檔案
  if (configFiles.length > 0) {
    report.push('### ⚙️ 配置檔案');
    configFiles.sort((a, b) => a.name.localeCompare(b.name)).forEach(file => {
      report.push(`#### ${file.name}`);
      if (file.functions.length === 0) {
        report.push('- (無函數)');
      } else {
        file.functions.sort().forEach(func => {
          report.push(`- ${func}()`);
        });
      }
      report.push('');
    });
  }
  
  // 測試檔案
  if (testFiles.length > 0) {
    report.push('### 🧪 測試檔案');
    testFiles.sort((a, b) => a.name.localeCompare(b.name)).forEach(file => {
      report.push(`#### ${file.name}`);
      if (file.functions.length === 0) {
        report.push('- (無函數)');
      } else {
        file.functions.sort().forEach(func => {
          report.push(`- ${func}()`);
        });
      }
      report.push('');
    });
  }
  
  // 其他檔案
  if (otherFiles.length > 0) {
    report.push('### 📄 其他檔案');
    otherFiles.sort((a, b) => a.name.localeCompare(b.name)).forEach(file => {
      report.push(`#### ${file.name}`);
      if (file.functions.length === 0) {
        report.push('- (無函數)');
      } else {
        file.functions.sort().forEach(func => {
          report.push(`- ${func}()`);
        });
      }
      report.push('');
    });
  }
  
  return report.join('\n');
}

/**
 * 開發流程整合檢查 - 修改代碼前執行
 */
function 修改前檢查_debug() {
  console.log('🔍 執行修改前檢查...');
  
  const quickCheck = 快速重複檢查_debug();
  
  if (quickCheck.duplicates === 0) {
    console.log('✅ 修改前檢查通過，可以安全進行代碼修改');
    return { safe: true, message: '無重複函數，可以安全修改' };
  } else {
    console.log('⚠️ 發現重複函數，建議先修復後再進行代碼修改');
    console.log('📋 重複函數詳情:');
    quickCheck.details.forEach(duplicate => {
      console.log(`  ❌ ${duplicate.functionName}: ${duplicate.files.join(', ')}`);
    });
    return { 
      safe: false, 
      message: `發現 ${quickCheck.duplicates} 個重複函數，建議先修復`,
      duplicates: quickCheck.details
    };
  }
}

/**
 * 開發流程整合檢查 - 修改代碼後執行
 */
function 修改後檢查_debug() {
  console.log('🔍 執行修改後檢查...');
  
  const fullCheck = 函數重複掃描器_debug();
  
  if (fullCheck.success && fullCheck.duplicates === 0 && fullCheck.forbidden === 0) {
    console.log('✅ 修改後檢查通過，代碼修改安全');
    return { 
      safe: true, 
      message: '無重複或禁止函數，代碼修改安全',
      stats: fullCheck
    };
  } else {
    console.log('⚠️ 修改後檢查發現問題，請檢查並修復');
    return { 
      safe: false, 
      message: `發現 ${fullCheck.duplicates} 個重複函數和 ${fullCheck.forbidden} 個禁止函數`,
      stats: fullCheck
    };
  }
}